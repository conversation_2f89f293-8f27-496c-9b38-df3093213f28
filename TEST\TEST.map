Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32l071xx.o(RESET) refers to startup_stm32l071xx.o(STACK) for __initial_sp
    startup_stm32l071xx.o(RESET) refers to startup_stm32l071xx.o(.text) for Reset_Handler
    startup_stm32l071xx.o(RESET) refers to stm32l0xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32l071xx.o(RESET) refers to stm32l0xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32l071xx.o(RESET) refers to port.o(i.SVC_Handler) for SVC_Handler
    startup_stm32l071xx.o(RESET) refers to port.o(.emb_text) for PendSV_Handler
    startup_stm32l071xx.o(RESET) refers to port.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32l071xx.o(RESET) refers to stm32l0xx_it.o(i.RTC_IRQHandler) for RTC_IRQHandler
    startup_stm32l071xx.o(RESET) refers to stm32l0xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32l071xx.o(RESET) refers to stm32l0xx_it.o(i.TIM6_IRQHandler) for TIM6_IRQHandler
    startup_stm32l071xx.o(RESET) refers to stm32l0xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32l071xx.o(RESET) refers to stm32l0xx_it.o(i.LPUART1_IRQHandler) for LPUART1_IRQHandler
    startup_stm32l071xx.o(.text) refers to system_stm32l0xx.o(i.SystemInit) for SystemInit
    startup_stm32l071xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32l0xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main.o(i.main) refers to stm32l0xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to adc.o(i.MX_ADC_Init) for MX_ADC_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to rtc.o(i.MX_RTC_Init) for MX_RTC_Init
    main.o(i.main) refers to spi.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main.o(i.main) refers to usart.o(i.MX_LPUART1_UART_Init) for MX_LPUART1_UART_Init
    main.o(i.main) refers to freertos.o(i.MX_FREERTOS_Init) for MX_FREERTOS_Init
    main.o(i.main) refers to cmsis_os.o(i.osKernelStart) for osKernelStart
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    freertos.o(i.MX_FREERTOS_Init) refers to memcpya.o(.text) for __aeabi_memcpy4
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os.o(i.osThreadCreate) for osThreadCreate
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.constdata) for .constdata
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.data) for .data
    freertos.o(i.StartAccelTask) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.StartAccelTask) refers to printfa.o(i.__0printf) for __2printf
    freertos.o(i.StartAccelTask) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    freertos.o(i.StartAccelTask) refers to adc.o(i.ADC_Calibrate) for ADC_Calibrate
    freertos.o(i.StartAccelTask) refers to lsm6ds3.o(i.LSM6DS3_Init) for LSM6DS3_Init
    freertos.o(i.StartAccelTask) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    freertos.o(i.StartAccelTask) refers to lsm6ds3.o(i.LSM6DS3_ReadData) for LSM6DS3_ReadData
    freertos.o(i.StartAccelTask) refers to ffltui.o(.text) for __aeabi_ui2f
    freertos.o(i.StartAccelTask) refers to fadd.o(.text) for __aeabi_fsub
    freertos.o(i.StartAccelTask) refers to fmul.o(.text) for __aeabi_fmul
    freertos.o(i.StartAccelTask) refers to f2d.o(.text) for __aeabi_f2d
    freertos.o(i.StartAccelTask) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.StartAccelTask) refers to i2c.o(.bss) for hi2c1
    freertos.o(i.StartAccelTask) refers to freertos.o(.bss) for .bss
    freertos.o(i.StartAccelTask) refers to adc.o(.bss) for hadc
    freertos.o(i.StartAccelTask) refers to freertos.o(.data) for .data
    freertos.o(i.StartFlashTask) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.StartGM20Task) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.StartGPSTask) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    freertos.o(i.StartGPSTask) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.StartPowerTask) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.vApplicationGetIdleTaskMemory) refers to freertos.o(.bss) for .bss
    freertos.o(.constdata) refers to freertos.o(.conststring) for .conststring
    freertos.o(.constdata) refers to freertos.o(i.StartGPSTask) for StartGPSTask
    freertos.o(.constdata) refers to freertos.o(.bss) for GPSTaskBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for GPSTaskControlBlock
    freertos.o(.constdata) refers to freertos.o(i.StartAccelTask) for StartAccelTask
    freertos.o(.constdata) refers to freertos.o(.bss) for AccelTaskBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for AccelTaskControlBlock
    freertos.o(.constdata) refers to freertos.o(i.StartGM20Task) for StartGM20Task
    freertos.o(.constdata) refers to freertos.o(.bss) for GM20TaskBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for GM20TaskControlBlock
    freertos.o(.constdata) refers to freertos.o(i.StartFlashTask) for StartFlashTask
    freertos.o(.constdata) refers to freertos.o(.bss) for FlashTaskBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for FlashTaskControlBlock
    freertos.o(.constdata) refers to freertos.o(i.StartPowerTask) for StartPowerTask
    freertos.o(.constdata) refers to freertos.o(.bss) for myPowerTaskBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for myPowerTaskControlBlock
    adc.o(i.ADC_Calibrate) refers to stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) for HAL_ADCEx_Calibration_Start
    adc.o(i.ADC_Calibrate) refers to adc.o(.bss) for .bss
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC_Init) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC_Init) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC_Init) refers to adc.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C1_Init) refers to stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_LPUART1_UART_Init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_LPUART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_LPUART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.fgetc) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    usart.o(i.fgetc) refers to usart.o(.bss) for .bss
    usart.o(i.fputc) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    rtc.o(i.HAL_RTC_MspDeInit) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    rtc.o(i.HAL_RTC_MspInit) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    rtc.o(i.HAL_RTC_MspInit) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    rtc.o(i.MX_RTC_Init) refers to memseta.o(.text) for __aeabi_memclr4
    rtc.o(i.MX_RTC_Init) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_Init) for HAL_RTC_Init
    rtc.o(i.MX_RTC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    rtc.o(i.MX_RTC_Init) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime) for HAL_RTC_SetTime
    rtc.o(i.MX_RTC_Init) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate) for HAL_RTC_SetDate
    rtc.o(i.MX_RTC_Init) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT) for HAL_RTCEx_SetWakeUpTimer_IT
    rtc.o(i.MX_RTC_Init) refers to rtc.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    spi.o(i.HAL_SPI_MspInit) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI1_Init) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI1_Init) refers to spi.o(.bss) for .bss
    stm32l0xx_it.o(i.DMA1_Channel1_IRQHandler) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32l0xx_it.o(i.DMA1_Channel1_IRQHandler) refers to adc.o(.bss) for hdma_adc
    stm32l0xx_it.o(i.LPUART1_IRQHandler) refers to stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32l0xx_it.o(i.LPUART1_IRQHandler) refers to usart.o(.bss) for hlpuart1
    stm32l0xx_it.o(i.RTC_IRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler) for HAL_RTCEx_WakeUpTimerIRQHandler
    stm32l0xx_it.o(i.RTC_IRQHandler) refers to rtc.o(.bss) for hrtc
    stm32l0xx_it.o(i.TIM6_IRQHandler) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32l0xx_it.o(i.TIM6_IRQHandler) refers to stm32l0xx_hal_timebase_tim.o(.bss) for htim6
    stm32l0xx_it.o(i.USART1_IRQHandler) refers to stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32l0xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32l0xx_hal_msp.o(i.HAL_MspInit) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetClockConfig) for HAL_RCC_GetClockConfig
    stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l0xx_hal_timebase_tim.o(.bss) for .bss
    stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32l0xx_hal.o(.data) for uwTickPrio
    stm32l0xx_hal_timebase_tim.o(i.HAL_ResumeTick) refers to stm32l0xx_hal_timebase_tim.o(.bss) for .bss
    stm32l0xx_hal_timebase_tim.o(i.HAL_SuspendTick) refers to stm32l0xx_hal_timebase_tim.o(.bss) for .bss
    lsm6ds3.o(i.LSM6DS3_ComplementaryFilter) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    lsm6ds3.o(i.LSM6DS3_ComplementaryFilter) refers to lsm6ds3.o(i.LSM6DS3_GetPitch) for LSM6DS3_GetPitch
    lsm6ds3.o(i.LSM6DS3_ComplementaryFilter) refers to lsm6ds3.o(i.LSM6DS3_GetRoll) for LSM6DS3_GetRoll
    lsm6ds3.o(i.LSM6DS3_ComplementaryFilter) refers to ffltui.o(.text) for __aeabi_ui2f
    lsm6ds3.o(i.LSM6DS3_ComplementaryFilter) refers to fdiv.o(.text) for __aeabi_fdiv
    lsm6ds3.o(i.LSM6DS3_ComplementaryFilter) refers to fmul.o(.text) for __aeabi_fmul
    lsm6ds3.o(i.LSM6DS3_ComplementaryFilter) refers to fadd.o(.text) for __aeabi_fadd
    lsm6ds3.o(i.LSM6DS3_GetPitch) refers to fmul.o(.text) for __aeabi_fmul
    lsm6ds3.o(i.LSM6DS3_GetPitch) refers to fadd.o(.text) for __aeabi_fadd
    lsm6ds3.o(i.LSM6DS3_GetPitch) refers to sqrtf.o(i.sqrtf) for sqrtf
    lsm6ds3.o(i.LSM6DS3_GetPitch) refers to atan2f.o(i.atan2f) for atan2f
    lsm6ds3.o(i.LSM6DS3_GetPitch) refers to fdiv.o(.text) for __aeabi_fdiv
    lsm6ds3.o(i.LSM6DS3_GetRoll) refers to fmul.o(.text) for __aeabi_fmul
    lsm6ds3.o(i.LSM6DS3_GetRoll) refers to fadd.o(.text) for __aeabi_fadd
    lsm6ds3.o(i.LSM6DS3_GetRoll) refers to sqrtf.o(i.sqrtf) for sqrtf
    lsm6ds3.o(i.LSM6DS3_GetRoll) refers to atan2f.o(i.atan2f) for atan2f
    lsm6ds3.o(i.LSM6DS3_GetRoll) refers to fdiv.o(.text) for __aeabi_fdiv
    lsm6ds3.o(i.LSM6DS3_Init) refers to lsm6ds3.o(i.WriteReg) for WriteReg
    lsm6ds3.o(i.LSM6DS3_Init) refers to lsm6ds3.o(i.ReadRegs) for ReadRegs
    lsm6ds3.o(i.LSM6DS3_Init) refers to printfa.o(i.__0printf) for __2printf
    lsm6ds3.o(i.LSM6DS3_InitAttitude) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    lsm6ds3.o(i.LSM6DS3_ReadData) refers to lsm6ds3.o(i.ReadRegs) for ReadRegs
    lsm6ds3.o(i.LSM6DS3_ReadData) refers to fflti.o(.text) for __aeabi_i2f
    lsm6ds3.o(i.LSM6DS3_ReadData) refers to fmul.o(.text) for __aeabi_fmul
    lsm6ds3.o(i.LSM6DS3_ReadData) refers to fscalb.o(.text) for __ARM_scalbnf
    lsm6ds3.o(i.LSM6DS3_ReadData) refers to fadd.o(.text) for __aeabi_fadd
    lsm6ds3.o(i.ReadRegs) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    lsm6ds3.o(i.WriteReg) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    stm32l0xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32l0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l0xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l0xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32l0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l0xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32l0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l0xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32l0xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32l0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32l0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32l0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32l0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32l0xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32l0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32l0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l0xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32l0xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l0xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32l0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32l0xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32l0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32l0xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32l0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32l0xx_hal_tim.o(i.TIM_DMAError) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32l0xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32l0xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32l0xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32l0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l0xx_hal_tim.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32l0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l0xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l0xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32l0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l0xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32l0xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l0xx_hal_adc.o(i.ADC_DMAError) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l0xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_adc.o(i.ADC_Disable) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.ADC_Enable) refers to stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond) for ADC_DelayMicroSecond
    stm32l0xx_hal_adc.o(i.ADC_Enable) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond) for ADC_DelayMicroSecond
    stm32l0xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l0xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32l0xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32l0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l0xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32l0xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32l0xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32l0xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_EnableVREFINT) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_EnableVREFINTTempSensor) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal.o(i.HAL_DeInit) refers to stm32l0xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32l0xx_hal.o(i.HAL_Delay) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal.o(i.HAL_Delay) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_GetTick) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_GetTickFreq) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_GetTickPrio) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_IncTick) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_Init) refers to stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal.o(i.HAL_Init) refers to stm32l0xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32l0xx_hal.o(i.HAL_InitTick) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal.o(i.HAL_InitTick) refers to stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32l0xx_hal.o(i.HAL_InitTick) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l0xx_hal.o(i.HAL_InitTick) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_InitTick) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal.o(i.HAL_SetTickFreq) refers to stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal.o(i.HAL_SetTickFreq) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32l0xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32l0xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l0xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32l0xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32l0xx_hal_i2c.o(i.I2C_DMAError) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l0xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32l0xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_ITError) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_ITError) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_ITError) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32l0xx_hal_i2c.o(i.I2C_ITError) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_i2c.o(i.I2C_ITError) refers to stm32l0xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITError) refers to stm32l0xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l0xx_hal_i2c.o(i.I2C_ITError) refers to stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l0xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l0xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l0xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l0xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32l0xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32l0xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32l0xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32l0xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l0xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l0xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l0xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l0xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32l0xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l0xx.o(.constdata) for AHBPrescTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l0xx_hal.o(.data) for uwTickPrio
    stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l0xx_hal.o(.data) for uwTickPrio
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l0xx.o(.constdata) for APBPrescTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l0xx.o(.constdata) for APBPrescTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to system_stm32l0xx.o(.constdata) for PLLMulTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to memseta.o(.text) for __aeabi_memclr4
    stm32l0xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l0xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l0xx.o(.constdata) for AHBPrescTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l0xx_hal.o(.data) for uwTickPrio
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_flash_ramfunc.o(i.FLASHRAM_SetErrorCode) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ramfunc.o(i.FLASHRAM_WaitForLastOperation) refers to stm32l0xx_hal_flash_ramfunc.o(i.FLASHRAM_SetErrorCode) for FLASHRAM_SetErrorCode
    stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EraseParallelPage) refers to stm32l0xx_hal_flash_ramfunc.o(i.FLASHRAM_WaitForLastOperation) for FLASHRAM_WaitForLastOperation
    stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_GetError) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_HalfPageProgram) refers to stm32l0xx_hal_flash_ramfunc.o(i.FLASHRAM_WaitForLastOperation) for FLASHRAM_WaitForLastOperation
    stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_ProgramParallelHalfPage) refers to stm32l0xx_hal_flash_ramfunc.o(i.FLASHRAM_WaitForLastOperation) for FLASHRAM_WaitForLastOperation
    stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32l0xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig) for FLASH_OB_ProtectedSectorsConfig
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Erase) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Erase) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Program) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Program) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig) for FLASH_OB_ProtectedSectorsConfig
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32l0xx_hal_dma.o(i.HAL_DMA_DeInit) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_dma.o(i.HAL_DMA_Init) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32l0xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32l0xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l0xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback) refers to stm32l0xx_hal_tim.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32l0xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32l0xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32l0xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32l0xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l0xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l0xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l0xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l0xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l0xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l0xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32l0xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32l0xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l0xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l0xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32l0xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32l0xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l0xx_hal_uart.o(i.UART_DMAError) refers to stm32l0xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l0xx_hal_uart.o(i.UART_DMAError) refers to stm32l0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l0xx_hal_uart.o(i.UART_DMAError) refers to stm32l0xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l0xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l0xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l0xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l0xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to stm32l0xx_hal_tim.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32l0xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32l0xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32l0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l0xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmAEventCallback) for HAL_RTC_AlarmAEventCallback
    stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_AlarmBEventCallback) for HAL_RTCEx_AlarmBEventCallback
    stm32l0xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32l0xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to rtc.o(i.HAL_RTC_MspDeInit) for HAL_RTC_MspDeInit
    stm32l0xx_hal_rtc.o(i.HAL_RTC_DeactivateAlarm) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.HAL_RTC_GetAlarm) refers to stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32l0xx_hal_rtc.o(i.HAL_RTC_GetDate) refers to stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32l0xx_hal_rtc.o(i.HAL_RTC_GetTime) refers to stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32l0xx_hal_rtc.o(i.HAL_RTC_Init) refers to rtc.o(i.HAL_RTC_MspInit) for HAL_RTC_MspInit
    stm32l0xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32l0xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_PollForAlarmAEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32l0xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32l0xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.RTC_ExitInitMode) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock) refers to stm32l0xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateWakeUpTimer) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_GetTimeStamp) refers to stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForAlarmBEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper1Event) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper2Event) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper3Event) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTimeStampEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForWakeUpTimerEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock) refers to stm32l0xx_hal_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSmoothCalib) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampEventCallback) for HAL_RTCEx_TimeStampEventCallback
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper1EventCallback) for HAL_RTCEx_Tamper1EventCallback
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper2EventCallback) for HAL_RTCEx_Tamper2EventCallback
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper3EventCallback) for HAL_RTCEx_Tamper3EventCallback
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback) for HAL_RTCEx_WakeUpTimerEventCallback
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l0xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l0xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l0xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l0xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l0xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32l0xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l0xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32l0xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32l0xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l0xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l0xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32l0xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l0xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l0xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l0xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l0xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l0xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l0xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l0xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l0xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l0xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32l0xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l0xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l0xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l0xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l0xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l0xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l0xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l0xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l0xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l0xx_hal_spi.o(i.SPI_DMAError) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l0xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l0xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l0xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l0xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l0xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l0xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l0xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l0xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l0xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l0xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32l0xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32l0xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32l0xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l0xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32l0xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l0xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32l0xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l0xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32l0xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l0xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    system_stm32l0xx.o(i.SystemCoreClockUpdate) refers to uidiv.o(.text) for __aeabi_uidivmod
    system_stm32l0xx.o(i.SystemCoreClockUpdate) refers to system_stm32l0xx.o(.data) for .data
    system_stm32l0xx.o(i.SystemCoreClockUpdate) refers to system_stm32l0xx.o(.constdata) for .constdata
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupCreateStatic) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupGetBitsFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortYield) for vPortYield
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortYield) for vPortYield
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.pcQueueGetName) refers to queue.o(.bss) for .bss
    queue.o(i.prvCopyDataFromQueue) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.prvInitialiseMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueAddToRegistry) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueUnregisterQueue) refers to queue.o(.bss) for .bss
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericCreateStatic) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortYield) for vPortYield
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortYield) for vPortYield
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortYield) for vPortYield
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortYield) for vPortYield
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortYield) for vPortYield
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memseta.o(.text) for memset
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to memseta.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferGenericCreateStatic) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvIdleTask) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvIdleTask) refers to tasks.o(.data) for .data
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for .data
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for .bss
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGetFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskResume) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to freertos.o(i.vApplicationGetIdleTaskMemory) for vApplicationGetIdleTaskMemory
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to stm32l0xx_hal_tim.o(i.__ARM_common_switch8) for __ARM_common_switch8
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    tasks.o(i.xTaskGenericNotifyFromISR) refers to stm32l0xx_hal_tim.o(i.__ARM_common_switch8) for __ARM_common_switch8
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeAll) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortYield) for vPortYield
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeFromISR) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for .bss
    cmsis_os.o(i.osDelay) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    cmsis_os.o(i.osKernelRunning) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os.o(i.osKernelStart) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    cmsis_os.o(i.osKernelSysTick) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osKernelSysTick) refers to tasks.o(i.xTaskGetTickCountFromISR) for xTaskGetTickCountFromISR
    cmsis_os.o(i.osKernelSysTick) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os.o(i.osMailAlloc) refers to cmsis_os.o(i.osPoolAlloc) for osPoolAlloc
    cmsis_os.o(i.osMailCAlloc) refers to cmsis_os.o(i.osMailAlloc) for osMailAlloc
    cmsis_os.o(i.osMailCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os.o(i.osMailCreate) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os.o(i.osMailCreate) refers to cmsis_os.o(i.osPoolCreate) for osPoolCreate
    cmsis_os.o(i.osMailCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os.o(i.osMailFree) refers to cmsis_os.o(i.osPoolFree) for osPoolFree
    cmsis_os.o(i.osMailGet) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMailGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os.o(i.osMailGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os.o(i.osMailPut) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMailPut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os.o(i.osMailPut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osMessageAvailableSpace) refers to queue.o(i.uxQueueSpacesAvailable) for uxQueueSpacesAvailable
    cmsis_os.o(i.osMessageCreate) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os.o(i.osMessageCreate) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os.o(i.osMessageDelete) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMessageDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os.o(i.osMessageGet) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMessageGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os.o(i.osMessageGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os.o(i.osMessagePeek) refers to queue.o(i.xQueuePeek) for xQueuePeek
    cmsis_os.o(i.osMessagePut) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMessagePut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os.o(i.osMessagePut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osMessageWaiting) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMessageWaiting) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os.o(i.osMessageWaiting) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os.o(i.osMutexCreate) refers to queue.o(i.xQueueCreateMutexStatic) for xQueueCreateMutexStatic
    cmsis_os.o(i.osMutexCreate) refers to queue.o(i.xQueueCreateMutex) for xQueueCreateMutex
    cmsis_os.o(i.osMutexDelete) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMutexDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os.o(i.osMutexRelease) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMutexRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os.o(i.osMutexRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osMutexWait) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMutexWait) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os.o(i.osMutexWait) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os.o(i.osPoolAlloc) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osPoolAlloc) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    cmsis_os.o(i.osPoolAlloc) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os.o(i.osPoolAlloc) refers to uidiv.o(.text) for __aeabi_uidivmod
    cmsis_os.o(i.osPoolAlloc) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os.o(i.osPoolCAlloc) refers to cmsis_os.o(i.osPoolAlloc) for osPoolAlloc
    cmsis_os.o(i.osPoolCAlloc) refers to memseta.o(.text) for __aeabi_memclr
    cmsis_os.o(i.osPoolCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os.o(i.osPoolCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os.o(i.osPoolFree) refers to uidiv.o(.text) for __aeabi_uidivmod
    cmsis_os.o(i.osSemaphoreCreate) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os.o(i.osSemaphoreCreate) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os.o(i.osSemaphoreCreate) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osSemaphoreDelete) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os.o(i.osSemaphoreRelease) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osSemaphoreWait) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSemaphoreWait) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os.o(i.osSemaphoreWait) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os.o(i.osSignalSet) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSignalSet) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    cmsis_os.o(i.osSignalSet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os.o(i.osSignalWait) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSignalWait) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    cmsis_os.o(i.osSystickHandler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os.o(i.osSystickHandler) refers to port.o(i.SysTick_Handler) for SysTick_Handler
    cmsis_os.o(i.osThreadCreate) refers to cmsis_os.o(i.makeFreeRtosPriority) for makeFreeRtosPriority
    cmsis_os.o(i.osThreadCreate) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    cmsis_os.o(i.osThreadCreate) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    cmsis_os.o(i.osThreadGetId) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os.o(i.osThreadGetPriority) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGetFromISR) for uxTaskPriorityGetFromISR
    cmsis_os.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGet) for uxTaskPriorityGet
    cmsis_os.o(i.osThreadGetPriority) refers to cmsis_os.o(i.makeCmsisPriority) for makeCmsisPriority
    cmsis_os.o(i.osThreadResume) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osThreadResume) refers to tasks.o(i.xTaskResumeFromISR) for xTaskResumeFromISR
    cmsis_os.o(i.osThreadResume) refers to tasks.o(i.vTaskResume) for vTaskResume
    cmsis_os.o(i.osThreadResumeAll) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os.o(i.osThreadSetPriority) refers to cmsis_os.o(i.makeFreeRtosPriority) for makeFreeRtosPriority
    cmsis_os.o(i.osThreadSetPriority) refers to tasks.o(i.vTaskPrioritySet) for vTaskPrioritySet
    cmsis_os.o(i.osThreadSuspend) refers to tasks.o(i.vTaskSuspend) for vTaskSuspend
    cmsis_os.o(i.osThreadSuspendAll) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os.o(i.osThreadTerminate) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os.o(i.osThreadYield) refers to port.o(i.vPortYield) for vPortYield
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for .data
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(i.SysTick_Handler) refers to port.o(.emb_text) for ulSetInterruptMaskFromISR
    port.o(i.SysTick_Handler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    port.o(i.prvTaskExitError) refers to port.o(.data) for .data
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEndScheduler) refers to port.o(.data) for .data
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to uidiv.o(.text) for __aeabi_uidivmod
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvPortStartFirstTask
    port.o(i.xPortStartScheduler) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.atan2f) for atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to fdiv.o(.text) for __aeabi_fdiv
    atan2f.o(i.atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.atan2f) refers to fadd.o(.text) for __aeabi_fsub
    atan2f.o(i.atan2f) refers to fmul.o(.text) for __aeabi_fmul
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.__atan2f$lsc) for __atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to fdiv.o(.text) for __aeabi_fdiv
    atan2f_x.o(i.__atan2f$lsc) refers to fadd.o(.text) for __aeabi_fsub
    atan2f_x.o(i.__atan2f$lsc) refers to fmul.o(.text) for __aeabi_fmul
    atan2f_x.o(i.__atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.__atan2f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to fsqrt.o(.text) for _fsqrt
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to fsqrt.o(.text) for _fsqrt
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to fsqrt.o(.text) for _fsqrt
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers to fsqrt.o(.text) for _fsqrt
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    fscalb.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    funder.o(i.__mathlib_flt_divzero) refers to fdiv.o(.text) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_infnan) refers to fscalb.o(.text) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_infnan2) refers to fadd.o(.text) for __aeabi_fadd
    funder.o(i.__mathlib_flt_invalid) refers to fdiv.o(.text) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_overflow) refers to fscalb.o(.text) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_posinfnan) refers to fmul.o(.text) for __aeabi_fmul
    funder.o(i.__mathlib_flt_underflow) refers to fscalb.o(.text) for __ARM_scalbnf
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32l071xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32l071xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    fsqrt.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32l071xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing freertos.o(.rev16_text), (4 bytes).
    Removing freertos.o(.revsh_text), (4 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (48 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (80 bytes).
    Removing usart.o(i.fgetc), (32 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(i.HAL_RTC_MspDeInit), (40 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (40 bytes).
    Removing stm32l0xx_it.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_it.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_timebase_tim.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_timebase_tim.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_timebase_tim.o(i.HAL_ResumeTick), (20 bytes).
    Removing stm32l0xx_hal_timebase_tim.o(i.HAL_SuspendTick), (20 bytes).
    Removing lsm6ds3.o(.rev16_text), (4 bytes).
    Removing lsm6ds3.o(.revsh_text), (4 bytes).
    Removing lsm6ds3.o(i.LSM6DS3_ComplementaryFilter), (280 bytes).
    Removing lsm6ds3.o(i.LSM6DS3_GetPitch), (64 bytes).
    Removing lsm6ds3.o(i.LSM6DS3_GetRoll), (64 bytes).
    Removing lsm6ds3.o(i.LSM6DS3_InitAttitude), (28 bytes).
    Removing stm32l0xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (56 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Start), (80 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (160 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Stop), (32 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (56 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (44 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (220 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (204 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (296 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (296 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (102 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (102 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (52 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (94 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (356 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (130 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (84 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (152 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (128 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (36 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_GetChannelState), (30 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (300 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (56 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Init), (62 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start), (140 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (372 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (180 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Stop), (72 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (128 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (80 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (56 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Init), (62 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start), (136 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (372 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (176 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Stop), (72 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (160 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (128 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (210 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (52 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (78 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (52 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (70 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (60 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (80 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (208 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (56 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Init), (62 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start), (136 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (372 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (176 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (72 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (160 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (128 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (76 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (76 bytes).
    Removing stm32l0xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_DMACaptureCplt), (102 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (102 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_OC1_SetConfig), (48 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_OC2_SetConfig), (50 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_OC3_SetConfig), (54 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_OC4_SetConfig), (54 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_TI1_SetConfig), (88 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (38 bytes).
    Removing stm32l0xx_hal_tim.o(i.TIM_TI2_SetConfig), (52 bytes).
    Removing stm32l0xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (96 bytes).
    Removing stm32l0xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32l0xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_adc.o(i.ADC_ConversionStop), (90 bytes).
    Removing stm32l0xx_hal_adc.o(i.ADC_Disable), (104 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (156 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_DeInit), (180 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_IRQHandler), (202 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_PollForConversion), (200 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_PollForEvent), (126 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_Start), (92 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_Start_IT), (124 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (120 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32l0xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (12 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (94 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVREFINT), (16 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVREFINTTempSensor), (16 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_EnableVREFINT), (56 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_EnableVREFINTTempSensor), (56 bytes).
    Removing stm32l0xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DBG_DisableLowPowerConfig), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DBG_EnableLowPowerConfig), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DeInit), (40 bytes).
    Removing stm32l0xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_InitTick), (68 bytes).
    Removing stm32l0xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32l0xx_hal.o(i.HAL_ResumeTick), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SYSCFG_Disable_Lock_VREFINT), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SYSCFG_Enable_Lock_VREFINT), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SYSCFG_GetBootMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SYSCFG_VREFINT_OutputSelect), (24 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SuspendTick), (16 bytes).
    Removing stm32l0xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_DeInit), (52 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (50 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (94 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (116 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (272 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (276 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (124 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (316 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (156 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (400 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (228 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (312 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (312 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (160 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (252 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (156 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (248 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (156 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (294 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (200 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (88 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (372 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (212 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (376 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (216 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (370 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (260 bytes).
    Removing stm32l0xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (28 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_DMAAbort), (28 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_DMAError), (24 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (98 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (84 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (42 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (36 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_Disable_IRQ), (82 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_Enable_IRQ), (120 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_ITAddrCplt), (124 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_ITError), (276 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_ITListenCplt), (104 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_ITMasterCplt), (216 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (70 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_ITSlaveCplt), (448 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt), (106 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (288 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_Master_ISR_IT), (328 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (336 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (336 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (258 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (288 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_TreatErrorCallback), (38 bytes).
    Removing stm32l0xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (164 bytes).
    Removing stm32l0xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (28 bytes).
    Removing stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (66 bytes).
    Removing stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (28 bytes).
    Removing stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (66 bytes).
    Removing stm32l0xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit), (212 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (20 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (184 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (132 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (32 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (20 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (52 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (100 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (352 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (24 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(i.FLASHRAM_SetErrorCode), (144 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(i.FLASHRAM_WaitForLastOperation), (96 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (36 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (36 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EraseParallelPage), (88 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_GetError), (16 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_HalfPageProgram), (84 bytes).
    Removing stm32l0xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_ProgramParallelHalfPage), (136 bytes).
    Removing stm32l0xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode), (144 bytes).
    Removing stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation), (112 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (220 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_Lock), (28 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (32 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (68 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_Program), (60 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_Program_IT), (52 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_Unlock), (92 bytes).
    Removing stm32l0xx_hal_flash.o(.bss), (24 bytes).
    Removing stm32l0xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig), (152 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase), (44 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBGetConfig), (48 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram), (88 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_DisableFixedTimeProgram), (16 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_EnableFixedTimeProgram), (16 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Erase), (40 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Lock), (20 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Program), (88 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Unlock), (52 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (120 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (292 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP), (52 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP), (56 bytes).
    Removing stm32l0xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_DeInit), (240 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_LockPin), (36 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32l0xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_DeInit), (104 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (190 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (72 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_Start), (74 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (78 bytes).
    Removing stm32l0xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (104 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (96 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (100 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFastWakeUp), (16 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (76 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUltraLowPower), (16 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFastWakeUp), (16 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (24 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUltraLowPower), (16 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32l0xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (72 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_MPU_Disable), (16 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (20 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_MPU_Enable), (24 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (20 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (24 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (32 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (32 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (52 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (24 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (28 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config), (48 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32l0xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (100 bytes).
    Removing stm32l0xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32l0xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32l0xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (132 bytes).
    Removing stm32l0xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32l0xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32l0xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32l0xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32l0xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (148 bytes).
    Removing stm32l0xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (78 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (78 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init), (116 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_LIN_Init), (146 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_LIN_SendBreak), (44 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (58 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (58 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init), (142 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Abort), (252 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive), (186 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (204 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit), (112 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (116 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT), (292 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DMAPause), (130 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DMAResume), (126 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop), (154 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DeInit), (66 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (68 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (68 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Receive), (264 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Receive_DMA), (100 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Receive_IT), (100 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (24 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (172 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_IT), (120 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMAError), (76 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMAReceiveCplt), (178 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMARxAbortCallback), (66 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMARxHalfCplt), (56 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (40 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMATransmitCplt), (76 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMATxAbortCallback), (54 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_EndTxTransfer), (32 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_RxISR_16BIT), (216 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_RxISR_8BIT), (216 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA), (172 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT), (200 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_TxISR_16BIT), (94 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_TxISR_8BIT), (90 bytes).
    Removing stm32l0xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (52 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (136 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_DisableClockStopMode), (50 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (48 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_EnableClockStopMode), (50 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (48 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (332 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (108 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (106 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (152 bytes).
    Removing stm32l0xx_hal_rtc.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_rtc.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmAEventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler), (92 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_DST_Add1Hour), (32 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_DST_ClearStoreOperation), (32 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_DST_ReadStoreOperation), (12 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_DST_SetStoreOperation), (32 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_DST_Sub1Hour), (32 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_DeInit), (144 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_DeactivateAlarm), (184 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_GetAlarm), (132 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_GetDate), (68 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_GetState), (6 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_GetTime), (88 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_MspInit), (2 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_PollForAlarmAEvent), (72 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm), (376 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT), (408 bytes).
    Removing stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte), (16 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_AlarmBEventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPRead), (12 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPWrite), (12 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateCalibrationOutPut), (60 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock), (84 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateTamper), (96 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateTimeStamp), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateWakeUpTimer), (122 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DisableBypassShadow), (60 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_EnableBypassShadow), (60 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_GetTimeStamp), (156 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_GetWakeUpTimer), (8 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForAlarmBEvent), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper1Event), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper2Event), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper3Event), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTimeStampEvent), (92 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForWakeUpTimerEvent), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetCalibrationOutPut), (78 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock), (84 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSmoothCalib), (126 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift), (152 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTamper), (240 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTamper_IT), (232 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTimeStamp), (108 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTimeStamp_IT), (140 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer), (216 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper1EventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper2EventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper3EventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler), (184 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampEventCallback), (2 bytes).
    Removing stm32l0xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_Abort), (292 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_Abort_IT), (308 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_DMAPause), (34 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_DMAResume), (34 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_DMAStop), (72 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_DeInit), (48 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_IRQHandler), (248 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_Receive), (330 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (224 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_Receive_IT), (172 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit), (350 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (494 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (268 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (160 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (196 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (144 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (50 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (50 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (50 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (50 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_AbortRx_ISR), (92 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (148 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_CloseRx_ISR), (80 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_CloseTx_ISR), (132 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMAReceiveCplt), (110 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMARxAbortCallback), (104 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMATransmitCplt), (104 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (92 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_DMATxAbortCallback), (124 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_EndRxTransaction), (92 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_EndRxTxTransaction), (112 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_RxISR_16BIT), (34 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_RxISR_8BIT), (34 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_TxISR_16BIT), (34 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_TxISR_8BIT), (34 bytes).
    Removing stm32l0xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout), (184 bytes).
    Removing system_stm32l0xx.o(.rev16_text), (4 bytes).
    Removing system_stm32l0xx.o(.revsh_text), (4 bytes).
    Removing system_stm32l0xx.o(i.SystemCoreClockUpdate), (156 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (22 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (8 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (60 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (8 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (40 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (28 bytes).
    Removing event_groups.o(i.xEventGroupCreateStatic), (34 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (18 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (116 bytes).
    Removing event_groups.o(i.xEventGroupSync), (164 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (204 bytes).
    Removing queue.o(i.pcQueueGetName), (40 bytes).
    Removing queue.o(i.prvCopyDataFromQueue), (38 bytes).
    Removing queue.o(i.prvCopyDataToQueue), (112 bytes).
    Removing queue.o(i.prvInitialiseMutex), (24 bytes).
    Removing queue.o(i.prvInitialiseNewQueue), (28 bytes).
    Removing queue.o(i.prvIsQueueEmpty), (28 bytes).
    Removing queue.o(i.prvUnlockQueue), (112 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (24 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (12 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (28 bytes).
    Removing queue.o(i.vQueueAddToRegistry), (36 bytes).
    Removing queue.o(i.vQueueDelete), (30 bytes).
    Removing queue.o(i.vQueueUnregisterQueue), (40 bytes).
    Removing queue.o(i.xQueueCreateMutex), (22 bytes).
    Removing queue.o(i.xQueueCreateMutexStatic), (26 bytes).
    Removing queue.o(i.xQueueGenericCreate), (60 bytes).
    Removing queue.o(i.xQueueGenericCreateStatic), (64 bytes).
    Removing queue.o(i.xQueueGenericReset), (108 bytes).
    Removing queue.o(i.xQueueGenericSend), (280 bytes).
    Removing queue.o(i.xQueueGenericSendFromISR), (140 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (116 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (22 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (24 bytes).
    Removing queue.o(i.xQueuePeek), (258 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (76 bytes).
    Removing queue.o(i.xQueueReceive), (258 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (120 bytes).
    Removing queue.o(i.xQueueSemaphoreTake), (320 bytes).
    Removing queue.o(.bss), (64 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (18 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (46 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (106 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (58 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (88 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (62 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (30 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (16 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (76 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreateStatic), (78 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (24 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (38 bytes).
    Removing stream_buffer.o(i.xStreamBufferNextMessageLengthBytes), (54 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (172 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (58 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (110 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (58 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (210 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (58 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (110 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (32 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (28 bytes).
    Removing tasks.o(i.pcTaskGetName), (24 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (44 bytes).
    Removing tasks.o(i.pvTaskIncrementMutexHeldCount), (24 bytes).
    Removing tasks.o(i.ulTaskNotifyTake), (96 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (32 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (32 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskDelete), (120 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (20 bytes).
    Removing tasks.o(i.vTaskInternalSetTimeOutState), (16 bytes).
    Removing tasks.o(i.vTaskMissedYield), (12 bytes).
    Removing tasks.o(i.vTaskNotifyGiveFromISR), (136 bytes).
    Removing tasks.o(i.vTaskPlaceOnEventList), (36 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (60 bytes).
    Removing tasks.o(i.vTaskPriorityDisinheritAfterTimeout), (112 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (148 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (92 bytes).
    Removing tasks.o(i.vTaskResume), (92 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (36 bytes).
    Removing tasks.o(i.vTaskSuspend), (136 bytes).
    Removing tasks.o(i.xTaskCheckForTimeOut), (92 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (176 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (204 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetSchedulerState), (32 bytes).
    Removing tasks.o(i.xTaskGetTickCount), (12 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (12 bytes).
    Removing tasks.o(i.xTaskNotifyStateClear), (48 bytes).
    Removing tasks.o(i.xTaskNotifyWait), (124 bytes).
    Removing tasks.o(i.xTaskPriorityDisinherit), (104 bytes).
    Removing tasks.o(i.xTaskPriorityInherit), (120 bytes).
    Removing tasks.o(i.xTaskRemoveFromEventList), (100 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (108 bytes).
    Removing cmsis_os.o(.rev16_text), (4 bytes).
    Removing cmsis_os.o(.revsh_text), (4 bytes).
    Removing cmsis_os.o(i.inHandlerMode), (12 bytes).
    Removing cmsis_os.o(i.makeCmsisPriority), (14 bytes).
    Removing cmsis_os.o(i.osAbortDelay), (4 bytes).
    Removing cmsis_os.o(i.osDelayUntil), (4 bytes).
    Removing cmsis_os.o(i.osKernelRunning), (18 bytes).
    Removing cmsis_os.o(i.osKernelSysTick), (22 bytes).
    Removing cmsis_os.o(i.osMailAlloc), (14 bytes).
    Removing cmsis_os.o(i.osMailCAlloc), (32 bytes).
    Removing cmsis_os.o(i.osMailCreate), (92 bytes).
    Removing cmsis_os.o(i.osMailFree), (18 bytes).
    Removing cmsis_os.o(i.osMailGet), (136 bytes).
    Removing cmsis_os.o(i.osMailPut), (88 bytes).
    Removing cmsis_os.o(i.osMessageAvailableSpace), (8 bytes).
    Removing cmsis_os.o(i.osMessageCreate), (40 bytes).
    Removing cmsis_os.o(i.osMessageDelete), (26 bytes).
    Removing cmsis_os.o(i.osMessageGet), (140 bytes).
    Removing cmsis_os.o(i.osMessagePeek), (76 bytes).
    Removing cmsis_os.o(i.osMessagePut), (88 bytes).
    Removing cmsis_os.o(i.osMessageWaiting), (26 bytes).
    Removing cmsis_os.o(i.osMutexCreate), (24 bytes).
    Removing cmsis_os.o(i.osMutexDelete), (26 bytes).
    Removing cmsis_os.o(i.osMutexRelease), (76 bytes).
    Removing cmsis_os.o(i.osMutexWait), (100 bytes).
    Removing cmsis_os.o(i.osPoolAlloc), (102 bytes).
    Removing cmsis_os.o(i.osPoolCAlloc), (20 bytes).
    Removing cmsis_os.o(i.osPoolCreate), (94 bytes).
    Removing cmsis_os.o(i.osPoolFree), (48 bytes).
    Removing cmsis_os.o(i.osRecursiveMutexCreate), (4 bytes).
    Removing cmsis_os.o(i.osRecursiveMutexRelease), (4 bytes).
    Removing cmsis_os.o(i.osRecursiveMutexWait), (4 bytes).
    Removing cmsis_os.o(i.osSemaphoreCreate), (64 bytes).
    Removing cmsis_os.o(i.osSemaphoreDelete), (26 bytes).
    Removing cmsis_os.o(i.osSemaphoreGetCount), (8 bytes).
    Removing cmsis_os.o(i.osSemaphoreRelease), (76 bytes).
    Removing cmsis_os.o(i.osSemaphoreWait), (100 bytes).
    Removing cmsis_os.o(i.osSignalSet), (88 bytes).
    Removing cmsis_os.o(i.osSignalWait), (90 bytes).
    Removing cmsis_os.o(i.osSystickHandler), (16 bytes).
    Removing cmsis_os.o(i.osThreadGetId), (8 bytes).
    Removing cmsis_os.o(i.osThreadGetPriority), (30 bytes).
    Removing cmsis_os.o(i.osThreadList), (4 bytes).
    Removing cmsis_os.o(i.osThreadResume), (44 bytes).
    Removing cmsis_os.o(i.osThreadResumeAll), (18 bytes).
    Removing cmsis_os.o(i.osThreadSetPriority), (22 bytes).
    Removing cmsis_os.o(i.osThreadSuspend), (10 bytes).
    Removing cmsis_os.o(i.osThreadSuspendAll), (10 bytes).
    Removing cmsis_os.o(i.osThreadTerminate), (10 bytes).
    Removing cmsis_os.o(i.osThreadYield), (10 bytes).
    Removing cmsis_os.o(i.osTimerCreate), (4 bytes).
    Removing cmsis_os.o(i.osTimerDelete), (4 bytes).
    Removing cmsis_os.o(i.osTimerStart), (4 bytes).
    Removing cmsis_os.o(i.osTimerStop), (4 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (24 bytes).
    Removing fdiv.o(.text), (124 bytes).
    Removing fsqrt.o(.text), (88 bytes).

760 unused section(s) (total 55170 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/rtc.c                        0x00000000   Number         0  rtc.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32l0xx_hal_msp.c          0x00000000   Number         0  stm32l0xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32l0xx_hal_timebase_tim.c 0x00000000   Number         0  stm32l0xx_hal_timebase_tim.o ABSOLUTE
    ../Core/Src/stm32l0xx_it.c               0x00000000   Number         0  stm32l0xx_it.o ABSOLUTE
    ../Core/Src/system_stm32l0xx.c           0x00000000   Number         0  system_stm32l0xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal.c 0x00000000   Number         0  stm32l0xx_hal.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_adc.c 0x00000000   Number         0  stm32l0xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_adc_ex.c 0x00000000   Number         0  stm32l0xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_cortex.c 0x00000000   Number         0  stm32l0xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_dma.c 0x00000000   Number         0  stm32l0xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_exti.c 0x00000000   Number         0  stm32l0xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_flash.c 0x00000000   Number         0  stm32l0xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_flash_ex.c 0x00000000   Number         0  stm32l0xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l0xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_gpio.c 0x00000000   Number         0  stm32l0xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_i2c.c 0x00000000   Number         0  stm32l0xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l0xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_pwr.c 0x00000000   Number         0  stm32l0xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l0xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_rcc.c 0x00000000   Number         0  stm32l0xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l0xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_rtc.c 0x00000000   Number         0  stm32l0xx_hal_rtc.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_rtc_ex.c 0x00000000   Number         0  stm32l0xx_hal_rtc_ex.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_spi.c 0x00000000   Number         0  stm32l0xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_tim.c 0x00000000   Number         0  stm32l0xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_tim_ex.c 0x00000000   Number         0  stm32l0xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_uart.c 0x00000000   Number         0  stm32l0xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_uart_ex.c 0x00000000   Number         0  stm32l0xx_hal_uart_ex.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS/cmsis_os.c 0x00000000   Number         0  cmsis_os.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/list.c 0x00000000   Number         0  list.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM0/port.c 0x00000000   Number         0  port.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/queue.c 0x00000000   Number         0  queue.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/tasks.c 0x00000000   Number         0  tasks.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/timers.c 0x00000000   Number         0  timers.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memset.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  fscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  fsqrt.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\lsm6ds3.c                    0x00000000   Number         0  lsm6ds3.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\rtc.c                        0x00000000   Number         0  rtc.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32l0xx_hal_msp.c          0x00000000   Number         0  stm32l0xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32l0xx_hal_timebase_tim.c 0x00000000   Number         0  stm32l0xx_hal_timebase_tim.o ABSOLUTE
    ..\Core\Src\stm32l0xx_it.c               0x00000000   Number         0  stm32l0xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32l0xx.c           0x00000000   Number         0  system_stm32l0xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal.c 0x00000000   Number         0  stm32l0xx_hal.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_adc.c 0x00000000   Number         0  stm32l0xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_adc_ex.c 0x00000000   Number         0  stm32l0xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_cortex.c 0x00000000   Number         0  stm32l0xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_dma.c 0x00000000   Number         0  stm32l0xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_exti.c 0x00000000   Number         0  stm32l0xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_flash.c 0x00000000   Number         0  stm32l0xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_flash_ex.c 0x00000000   Number         0  stm32l0xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l0xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_gpio.c 0x00000000   Number         0  stm32l0xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_i2c.c 0x00000000   Number         0  stm32l0xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l0xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_pwr.c 0x00000000   Number         0  stm32l0xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l0xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_rcc.c 0x00000000   Number         0  stm32l0xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l0xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_rtc.c 0x00000000   Number         0  stm32l0xx_hal_rtc.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_rtc_ex.c 0x00000000   Number         0  stm32l0xx_hal_rtc_ex.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_spi.c 0x00000000   Number         0  stm32l0xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_tim.c 0x00000000   Number         0  stm32l0xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_tim_ex.c 0x00000000   Number         0  stm32l0xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_uart.c 0x00000000   Number         0  stm32l0xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32L0xx_HAL_Driver\Src\stm32l0xx_hal_uart_ex.c 0x00000000   Number         0  stm32l0xx_hal_uart_ex.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS\cmsis_os.c 0x00000000   Number         0  cmsis_os.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM0\port.c 0x00000000   Number         0  port.o ABSOLUTE
    ..\\Core\\Src\\lsm6ds3.c                 0x00000000   Number         0  lsm6ds3.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32l071xx.s                    0x00000000   Number         0  startup_stm32l071xx.o ABSOLUTE
    RESET                                    0x08000000   Section      192  startup_stm32l071xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x080000d4   Section      120  port.o(.emb_text)
    .text                                    0x0800014c   Section       28  startup_stm32l071xx.o(.text)
    .text                                    0x08000168   Section        0  uidiv.o(.text)
    .text                                    0x08000194   Section        0  uldiv.o(.text)
    .text                                    0x080001f4   Section        0  memcpya.o(.text)
    .text                                    0x08000218   Section        0  memseta.o(.text)
    .text                                    0x0800023c   Section        0  fadd.o(.text)
    .text                                    0x080002ee   Section        0  fmul.o(.text)
    .text                                    0x08000368   Section        0  fscalb.o(.text)
    .text                                    0x08000380   Section        0  fflti.o(.text)
    .text                                    0x08000396   Section        0  ffltui.o(.text)
    .text                                    0x080003a4   Section        0  f2d.o(.text)
    .text                                    0x080003cc   Section        0  llshl.o(.text)
    .text                                    0x080003ec   Section        0  llushr.o(.text)
    .text                                    0x0800040e   Section        0  fepilogue.o(.text)
    .text                                    0x0800040e   Section        0  iusefp.o(.text)
    .text                                    0x08000490   Section        0  dadd.o(.text)
    .text                                    0x080005f4   Section        0  dmul.o(.text)
    .text                                    0x080006c4   Section        0  ddiv.o(.text)
    .text                                    0x080007b4   Section        0  dfixul.o(.text)
    .text                                    0x080007f4   Section       40  cdrcmple.o(.text)
    .text                                    0x0800081c   Section       36  init.o(.text)
    .text                                    0x08000840   Section        0  llsshr.o(.text)
    .text                                    0x08000866   Section        0  depilogue.o(.text)
    i.ADC_Calibrate                          0x08000924   Section        0  adc.o(i.ADC_Calibrate)
    i.ADC_DMAConvCplt                        0x08000934   Section        0  stm32l0xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x08000935   Thumb Code   110  stm32l0xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x080009a2   Section        0  stm32l0xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x080009a3   Thumb Code    26  stm32l0xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x080009bc   Section        0  stm32l0xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x080009bd   Thumb Code    10  stm32l0xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_DelayMicroSecond                   0x080009c8   Section        0  stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond)
    ADC_DelayMicroSecond                     0x080009c9   Thumb Code    28  stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond)
    i.ADC_Enable                             0x080009ec   Section        0  stm32l0xx_hal_adc.o(i.ADC_Enable)
    ADC_Enable                               0x080009ed   Thumb Code   106  stm32l0xx_hal_adc.o(i.ADC_Enable)
    i.DMA1_Channel1_IRQHandler               0x08000a5c   Section        0  stm32l0xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA_SetConfig                          0x08000a6c   Section        0  stm32l0xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08000a6d   Thumb Code    44  stm32l0xx_hal_dma.o(i.DMA_SetConfig)
    i.Error_Handler                          0x08000a98   Section        0  main.o(i.Error_Handler)
    i.HAL_ADCEx_Calibration_Start            0x08000a9c   Section        0  stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start)
    i.HAL_ADC_ConfigChannel                  0x08000b50   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08000be4   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x08000be6   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08000be8   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_Init                           0x08000bec   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08000db4   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08000e3c   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_DMA_Abort                          0x08000ed4   Section        0  stm32l0xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000f18   Section        0  stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08000f64   Section        0  stm32l0xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x0800100c   Section        0  stm32l0xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x0800109c   Section        0  stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_GPIO_Init                          0x08001104   Section        0  stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_TogglePin                     0x080012c0   Section        0  stm32l0xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x080012d0   Section        0  stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080012dc   Section        0  stm32l0xx_hal.o(i.HAL_GetTick)
    i.HAL_I2CEx_ConfigAnalogFilter           0x080012e8   Section        0  stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    i.HAL_I2CEx_ConfigDigitalFilter          0x08001332   Section        0  stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    i.HAL_I2C_Init                           0x0800137c   Section        0  stm32l0xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_IsDeviceReady                  0x08001438   Section        0  stm32l0xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady)
    i.HAL_I2C_Mem_Read                       0x0800152c   Section        0  stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    i.HAL_I2C_Mem_Write                      0x0800167c   Section        0  stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x080017c8   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08001824   Section        0  stm32l0xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001834   Section        0  stm32l0xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x0800185c   Section        0  stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080018f8   Section        0  stm32l0xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x0800191c   Section        0  stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001934   Section        0  stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_RCCEx_PeriphCLKConfig              0x0800193c   Section        0  stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08001ab8   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetClockConfig                 0x08001c54   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08001c94   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001cb4   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08001cd4   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001d4c   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_RTCEx_SetWakeUpTimer_IT            0x08002180   Section        0  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT)
    i.HAL_RTCEx_WakeUpTimerEventCallback     0x0800226c   Section        0  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback)
    i.HAL_RTCEx_WakeUpTimerIRQHandler        0x08002270   Section        0  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler)
    i.HAL_RTC_Init                           0x080022ac   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_Init)
    i.HAL_RTC_MspInit                        0x08002358   Section        0  rtc.o(i.HAL_RTC_MspInit)
    i.HAL_RTC_SetDate                        0x08002388   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate)
    i.HAL_RTC_SetTime                        0x08002424   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime)
    i.HAL_RTC_WaitForSynchro                 0x080024e4   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro)
    i.HAL_SPI_Init                           0x08002518   Section        0  stm32l0xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x080025d0   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_TIM_Base_Init                      0x08002628   Section        0  stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08002666   Section        0  stm32l0xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08002668   Section        0  stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_IC_CaptureCallback             0x080026c0   Section        0  stm32l0xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x080026c2   Section        0  stm32l0xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x080027be   Section        0  stm32l0xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x080027c0   Section        0  stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x080027c4   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x080027d8   Section        0  stm32l0xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x080027da   Section        0  stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UARTEx_WakeupCallback              0x080027dc   Section        0  stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_ErrorCallback                 0x080027de   Section        0  stm32l0xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080027e0   Section        0  stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002aa0   Section        0  stm32l0xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08002b0c   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Transmit                      0x08002bbc   Section        0  stm32l0xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08002c7c   Section        0  stm32l0xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08002c7e   Section        0  stm32l0xx_it.o(i.HardFault_Handler)
    i.I2C_Flush_TXDR                         0x08002c80   Section        0  stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR)
    I2C_Flush_TXDR                           0x08002c81   Thumb Code    34  stm32l0xx_hal_i2c.o(i.I2C_Flush_TXDR)
    i.I2C_IsErrorOccurred                    0x08002ca4   Section        0  stm32l0xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    I2C_IsErrorOccurred                      0x08002ca5   Thumb Code   266  stm32l0xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    i.I2C_RequestMemoryRead                  0x08002db4   Section        0  stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    I2C_RequestMemoryRead                    0x08002db5   Thumb Code    92  stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    i.I2C_RequestMemoryWrite                 0x08002e14   Section        0  stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08002e15   Thumb Code    92  stm32l0xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_TransferConfig                     0x08002e74   Section        0  stm32l0xx_hal_i2c.o(i.I2C_TransferConfig)
    I2C_TransferConfig                       0x08002e75   Thumb Code    38  stm32l0xx_hal_i2c.o(i.I2C_TransferConfig)
    i.I2C_WaitOnFlagUntilTimeout             0x08002ea0   Section        0  stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08002ea1   Thumb Code   114  stm32l0xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnSTOPFlagUntilTimeout         0x08002f12   Section        0  stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    I2C_WaitOnSTOPFlagUntilTimeout           0x08002f13   Thumb Code    86  stm32l0xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    i.I2C_WaitOnTXISFlagUntilTimeout         0x08002f68   Section        0  stm32l0xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    I2C_WaitOnTXISFlagUntilTimeout           0x08002f69   Thumb Code    90  stm32l0xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    i.LPUART1_IRQHandler                     0x08002fc4   Section        0  stm32l0xx_it.o(i.LPUART1_IRQHandler)
    i.LSM6DS3_Init                           0x08002fd4   Section        0  lsm6ds3.o(i.LSM6DS3_Init)
    i.LSM6DS3_ReadData                       0x08003094   Section        0  lsm6ds3.o(i.LSM6DS3_ReadData)
    i.MX_ADC_Init                            0x08003180   Section        0  adc.o(i.MX_ADC_Init)
    i.MX_DMA_Init                            0x08003220   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_FREERTOS_Init                       0x08003248   Section        0  freertos.o(i.MX_FREERTOS_Init)
    i.MX_GPIO_Init                           0x080032c8   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08003390   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_LPUART1_UART_Init                   0x080033e8   Section        0  usart.o(i.MX_LPUART1_UART_Init)
    i.MX_RTC_Init                            0x08003420   Section        0  rtc.o(i.MX_RTC_Init)
    i.MX_SPI1_Init                           0x080034b0   Section        0  spi.o(i.MX_SPI1_Init)
    i.MX_USART1_UART_Init                    0x080034f0   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.NMI_Handler                            0x08003528   Section        0  stm32l0xx_it.o(i.NMI_Handler)
    i.RTC_ByteToBcd2                         0x0800352a   Section        0  stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2)
    i.RTC_EnterInitMode                      0x08003540   Section        0  stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode)
    i.RTC_ExitInitMode                       0x08003586   Section        0  stm32l0xx_hal_rtc.o(i.RTC_ExitInitMode)
    i.RTC_IRQHandler                         0x080035b4   Section        0  stm32l0xx_it.o(i.RTC_IRQHandler)
    i.ReadRegs                               0x080035c4   Section        0  lsm6ds3.o(i.ReadRegs)
    i.SVC_Handler                            0x080035da   Section        0  port.o(i.SVC_Handler)
    i.StartAccelTask                         0x080035dc   Section        0  freertos.o(i.StartAccelTask)
    i.StartFlashTask                         0x080039d0   Section        0  freertos.o(i.StartFlashTask)
    i.StartGM20Task                          0x080039d8   Section        0  freertos.o(i.StartGM20Task)
    i.StartGPSTask                           0x080039e0   Section        0  freertos.o(i.StartGPSTask)
    i.StartPowerTask                         0x08003a00   Section        0  freertos.o(i.StartPowerTask)
    i.SysTick_Handler                        0x08003a08   Section        0  port.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08003a2c   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08003ac8   Section        0  system_stm32l0xx.o(i.SystemInit)
    i.TIM6_IRQHandler                        0x08003acc   Section        0  stm32l0xx_it.o(i.TIM6_IRQHandler)
    i.TIM_Base_SetConfig                     0x08003adc   Section        0  stm32l0xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_Base_SetConfig                       0x08003add   Thumb Code    98  stm32l0xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.UART_AdvFeatureConfig                  0x08003b4c   Section        0  stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x08003c18   Section        0  stm32l0xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x08003cdc   Section        0  stm32l0xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08003cdd   Thumb Code    16  stm32l0xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08003cec   Section        0  stm32l0xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08003ced   Thumb Code    96  stm32l0xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_SetConfig                         0x08003d4c   Section        0  stm32l0xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x08003f60   Section        0  stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08003ff8   Section        0  stm32l0xx_it.o(i.USART1_IRQHandler)
    i.WriteReg                               0x08004008   Section        0  lsm6ds3.o(i.WriteReg)
    WriteReg                                 0x08004009   Thumb Code    30  lsm6ds3.o(i.WriteReg)
    i.__0printf                              0x08004028   Section        0  printfa.o(i.__0printf)
    i.__ARM_clz                              0x08004048   Section        0  depilogue.o(i.__ARM_clz)
    i.__ARM_common_switch8                   0x08004076   Section        0  stm32l0xx_hal_tim.o(i.__ARM_common_switch8)
    i.__NVIC_SetPriority                     0x08004090   Section        0  stm32l0xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08004091   Thumb Code    60  stm32l0xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x080040d4   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080040e2   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080040e4   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080040f4   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080040f5   Thumb Code   344  printfa.o(i._fp_digits)
    i._printf_core                           0x08004268   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08004269   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08004954   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08004955   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08004974   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08004975   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i.fputc                                  0x080049a0   Section        0  usart.o(i.fputc)
    i.main                                   0x080049b8   Section        0  main.o(i.main)
    i.makeFreeRtosPriority                   0x080049ea   Section        0  cmsis_os.o(i.makeFreeRtosPriority)
    makeFreeRtosPriority                     0x080049eb   Thumb Code    12  cmsis_os.o(i.makeFreeRtosPriority)
    i.osDelay                                0x080049f6   Section        0  cmsis_os.o(i.osDelay)
    i.osKernelStart                          0x08004a06   Section        0  cmsis_os.o(i.osKernelStart)
    i.osThreadCreate                         0x08004a10   Section        0  cmsis_os.o(i.osThreadCreate)
    i.prvAddCurrentTaskToDelayedList         0x08004a68   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08004a69   Thumb Code    78  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x08004ac0   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x08004ac1   Thumb Code   176  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvDeleteTCB                           0x08004b78   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x08004b79   Thumb Code    38  tasks.o(i.prvDeleteTCB)
    i.prvHeapInit                            0x08004ba0   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x08004ba1   Thumb Code    60  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x08004be4   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x08004be5   Thumb Code    64  tasks.o(i.prvIdleTask)
    i.prvInitialiseNewTask                   0x08004c2c   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x08004c2d   Thumb Code   138  tasks.o(i.prvInitialiseNewTask)
    i.prvInsertBlockIntoFreeList             0x08004cb8   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x08004cb9   Thumb Code    72  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvResetNextTaskUnblockTime            0x08004d04   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x08004d05   Thumb Code    28  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvTaskExitError                       0x08004d24   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08004d25   Thumb Code    14  port.o(i.prvTaskExitError)
    i.pvPortMalloc                           0x08004d38   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxPortInitialiseStack                  0x08004de8   Section        0  port.o(i.pxPortInitialiseStack)
    i.uxListRemove                           0x08004e08   Section        0  list.o(i.uxListRemove)
    i.vApplicationGetIdleTaskMemory          0x08004e30   Section        0  freertos.o(i.vApplicationGetIdleTaskMemory)
    i.vListInitialise                        0x08004e44   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x08004e5a   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08004e60   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x08004e90   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x08004ea8   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08004ec0   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08004edc   Section        0  heap_4.o(i.vPortFree)
    i.vPortYield                             0x08004f20   Section        0  port.o(i.vPortYield)
    i.vTaskDelay                             0x08004f38   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskStartScheduler                    0x08004f68   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x08004fc0   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x08004fd0   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xPortStartScheduler                    0x08005028   Section        0  port.o(i.xPortStartScheduler)
    i.xTaskCreate                            0x08005078   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskCreateStatic                      0x080050ca   Section        0  tasks.o(i.xTaskCreateStatic)
    i.xTaskIncrementTick                     0x08005110   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskResumeAll                         0x080051cc   Section        0  tasks.o(i.xTaskResumeAll)
    .constdata                               0x08005278   Section      140  freertos.o(.constdata)
    .constdata                               0x08005304   Section       25  system_stm32l0xx.o(.constdata)
    .constdata                               0x0800531d   Section        8  system_stm32l0xx.o(.constdata)
    .conststring                             0x08005328   Section       56  freertos.o(.conststring)
    .data                                    0x20000000   Section       24  freertos.o(.data)
    cal_printed                              0x20000000   Data           1  freertos.o(.data)
    .data                                    0x20000018   Section       12  stm32l0xx_hal.o(.data)
    .data                                    0x20000024   Section        4  system_stm32l0xx.o(.data)
    .data                                    0x20000028   Section       60  tasks.o(.data)
    pxDelayedTaskList                        0x2000002c   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x20000030   Data           4  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x20000034   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x20000038   Data           4  tasks.o(.data)
    xTickCount                               0x2000003c   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x20000040   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x20000044   Data           4  tasks.o(.data)
    uxPendedTicks                            0x20000048   Data           4  tasks.o(.data)
    xYieldPending                            0x2000004c   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x20000050   Data           4  tasks.o(.data)
    uxTaskNumber                             0x20000054   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x20000058   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x2000005c   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x20000060   Data           4  tasks.o(.data)
    .data                                    0x20000064   Section       24  heap_4.o(.data)
    pxEnd                                    0x20000064   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x20000068   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x2000006c   Data           4  heap_4.o(.data)
    xBlockAllocatedBit                       0x20000070   Data           4  heap_4.o(.data)
    xStart                                   0x20000074   Data           8  heap_4.o(.data)
    .data                                    0x2000007c   Section        4  port.o(.data)
    uxCriticalNesting                        0x2000007c   Data           4  port.o(.data)
    .data                                    0x20000080   Section        4  stdout.o(.data)
    .bss                                     0x20000084   Section      636  freertos.o(.bss)
    xIdleTaskTCBBuffer                       0x200000ac   Data          84  freertos.o(.bss)
    xIdleStack                               0x20000100   Data         512  freertos.o(.bss)
    .bss                                     0x20000300   Section     2048  freertos.o(.bss)
    .bss                                     0x20000b00   Section       84  freertos.o(.bss)
    .bss                                     0x20000b54   Section     2048  freertos.o(.bss)
    .bss                                     0x20001354   Section       84  freertos.o(.bss)
    .bss                                     0x200013a8   Section      512  freertos.o(.bss)
    .bss                                     0x200015a8   Section       84  freertos.o(.bss)
    .bss                                     0x200015fc   Section      512  freertos.o(.bss)
    .bss                                     0x200017fc   Section       84  freertos.o(.bss)
    .bss                                     0x20001850   Section      512  freertos.o(.bss)
    .bss                                     0x20001a50   Section       84  freertos.o(.bss)
    .bss                                     0x20001aa4   Section      164  adc.o(.bss)
    .bss                                     0x20001b48   Section       84  i2c.o(.bss)
    .bss                                     0x20001b9c   Section      272  usart.o(.bss)
    .bss                                     0x20001cac   Section       36  rtc.o(.bss)
    .bss                                     0x20001cd0   Section       88  spi.o(.bss)
    .bss                                     0x20001d28   Section       64  stm32l0xx_hal_timebase_tim.o(.bss)
    .bss                                     0x20001d68   Section      240  tasks.o(.bss)
    pxReadyTasksLists                        0x20001d68   Data         140  tasks.o(.bss)
    xDelayedTaskList1                        0x20001df4   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x20001e08   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x20001e1c   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x20001e30   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x20001e44   Data          20  tasks.o(.bss)
    .bss                                     0x20001e58   Section     3072  heap_4.o(.bss)
    ucHeap                                   0x20001e58   Data        3072  heap_4.o(.bss)
    STACK                                    0x20002a58   Section     1024  startup_stm32l071xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000c0   Number         0  startup_stm32l071xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32l071xx.o(RESET)
    __Vectors_End                            0x080000c0   Data           0  startup_stm32l071xx.o(RESET)
    __main                                   0x080000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    __asm___6_port_c_39a90d8d__prvPortStartFirstTask 0x080000d5   Thumb Code    36  port.o(.emb_text)
    ulSetInterruptMaskFromISR                0x080000fd   Thumb Code     8  port.o(.emb_text)
    vClearInterruptMaskFromISR               0x08000105   Thumb Code     6  port.o(.emb_text)
    PendSV_Handler                           0x0800010b   Thumb Code    62  port.o(.emb_text)
    Reset_Handler                            0x0800014d   Thumb Code     8  startup_stm32l071xx.o(.text)
    ADC1_COMP_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    DMA1_Channel2_3_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    DMA1_Channel4_5_6_7_IRQHandler           0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    EXTI0_1_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    EXTI2_3_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    EXTI4_15_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    I2C1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    I2C2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    I2C3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    LPTIM1_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    TIM21_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    TIM22_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    USART2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    USART4_5_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32l071xx.o(.text)
    __aeabi_uidiv                            0x08000169   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000169   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08000195   Thumb Code    96  uldiv.o(.text)
    __aeabi_memcpy                           0x080001f5   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x080001f5   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x080001f5   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000219   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000219   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000219   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000227   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000227   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000227   Thumb Code     0  memseta.o(.text)
    memset                                   0x0800022b   Thumb Code    18  memseta.o(.text)
    __aeabi_fadd                             0x0800023d   Thumb Code   162  fadd.o(.text)
    __aeabi_fsub                             0x080002df   Thumb Code     8  fadd.o(.text)
    __aeabi_frsub                            0x080002e7   Thumb Code     8  fadd.o(.text)
    __aeabi_fmul                             0x080002ef   Thumb Code   122  fmul.o(.text)
    __ARM_scalbnf                            0x08000369   Thumb Code    24  fscalb.o(.text)
    scalbnf                                  0x08000369   Thumb Code     0  fscalb.o(.text)
    __aeabi_i2f                              0x08000381   Thumb Code    22  fflti.o(.text)
    __aeabi_ui2f                             0x08000397   Thumb Code    14  ffltui.o(.text)
    __aeabi_f2d                              0x080003a5   Thumb Code    40  f2d.o(.text)
    __aeabi_llsl                             0x080003cd   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x080003cd   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080003ed   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x080003ed   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x0800040f   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800040f   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x0800041f   Thumb Code   114  fepilogue.o(.text)
    __aeabi_dadd                             0x08000491   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x080005d9   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x080005e5   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x080005f5   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x080006c5   Thumb Code   234  ddiv.o(.text)
    __aeabi_d2ulz                            0x080007b5   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x080007f5   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x0800081d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0800081d   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000841   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x08000841   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x08000867   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x08000881   Thumb Code   164  depilogue.o(.text)
    ADC_Calibrate                            0x08000925   Thumb Code    12  adc.o(i.ADC_Calibrate)
    DMA1_Channel1_IRQHandler                 0x08000a5d   Thumb Code    10  stm32l0xx_it.o(i.DMA1_Channel1_IRQHandler)
    Error_Handler                            0x08000a99   Thumb Code     4  main.o(i.Error_Handler)
    HAL_ADCEx_Calibration_Start              0x08000a9d   Thumb Code   178  stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start)
    HAL_ADC_ConfigChannel                    0x08000b51   Thumb Code   140  stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08000be5   Thumb Code     2  stm32l0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08000be7   Thumb Code     2  stm32l0xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x08000be9   Thumb Code     2  stm32l0xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x08000bed   Thumb Code   446  stm32l0xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08000db5   Thumb Code   120  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08000e3d   Thumb Code   136  stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_DMA_Abort                            0x08000ed5   Thumb Code    68  stm32l0xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000f19   Thumb Code    76  stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08000f65   Thumb Code   168  stm32l0xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x0800100d   Thumb Code   132  stm32l0xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x0800109d   Thumb Code   104  stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x08001105   Thumb Code   412  stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x080012c1   Thumb Code    16  stm32l0xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x080012d1   Thumb Code    12  stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080012dd   Thumb Code     6  stm32l0xx_hal.o(i.HAL_GetTick)
    HAL_I2CEx_ConfigAnalogFilter             0x080012e9   Thumb Code    74  stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    HAL_I2CEx_ConfigDigitalFilter            0x08001333   Thumb Code    72  stm32l0xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    HAL_I2C_Init                             0x0800137d   Thumb Code   182  stm32l0xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_IsDeviceReady                    0x08001439   Thumb Code   234  stm32l0xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady)
    HAL_I2C_Mem_Read                         0x0800152d   Thumb Code   328  stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    HAL_I2C_Mem_Write                        0x0800167d   Thumb Code   326  stm32l0xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x080017c9   Thumb Code    80  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08001825   Thumb Code    12  stm32l0xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001835   Thumb Code    36  stm32l0xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x0800185d   Thumb Code   132  stm32l0xx_hal_timebase_tim.o(i.HAL_InitTick)
    HAL_MspInit                              0x080018f9   Thumb Code    32  stm32l0xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x0800191d   Thumb Code    18  stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001935   Thumb Code     8  stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_RCCEx_PeriphCLKConfig                0x0800193d   Thumb Code   368  stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08001ab9   Thumb Code   386  stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetClockConfig                   0x08001c55   Thumb Code    54  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08001c95   Thumb Code    20  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001cb5   Thumb Code    20  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001cd5   Thumb Code   100  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001d4d   Thumb Code  1074  stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_RTCEx_SetWakeUpTimer_IT              0x08002181   Thumb Code   224  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT)
    HAL_RTCEx_WakeUpTimerEventCallback       0x0800226d   Thumb Code     2  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback)
    HAL_RTCEx_WakeUpTimerIRQHandler          0x08002271   Thumb Code    52  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler)
    HAL_RTC_Init                             0x080022ad   Thumb Code   166  stm32l0xx_hal_rtc.o(i.HAL_RTC_Init)
    HAL_RTC_MspInit                          0x08002359   Thumb Code    40  rtc.o(i.HAL_RTC_MspInit)
    HAL_RTC_SetDate                          0x08002389   Thumb Code   152  stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate)
    HAL_RTC_SetTime                          0x08002425   Thumb Code   188  stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime)
    HAL_RTC_WaitForSynchro                   0x080024e5   Thumb Code    48  stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro)
    HAL_SPI_Init                             0x08002519   Thumb Code   182  stm32l0xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x080025d1   Thumb Code    80  spi.o(i.HAL_SPI_MspInit)
    HAL_TIM_Base_Init                        0x08002629   Thumb Code    62  stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08002667   Thumb Code     2  stm32l0xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08002669   Thumb Code    74  stm32l0xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_IC_CaptureCallback               0x080026c1   Thumb Code     2  stm32l0xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080026c3   Thumb Code   252  stm32l0xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x080027bf   Thumb Code     2  stm32l0xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x080027c1   Thumb Code     2  stm32l0xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x080027c5   Thumb Code    16  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x080027d9   Thumb Code     2  stm32l0xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x080027db   Thumb Code     2  stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_WakeupCallback                0x080027dd   Thumb Code     2  stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x080027df   Thumb Code     2  stm32l0xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080027e1   Thumb Code   692  stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002aa1   Thumb Code   106  stm32l0xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002b0d   Thumb Code   162  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x08002bbd   Thumb Code   192  stm32l0xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08002c7d   Thumb Code     2  stm32l0xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002c7f   Thumb Code     2  stm32l0xx_it.o(i.HardFault_Handler)
    LPUART1_IRQHandler                       0x08002fc5   Thumb Code    10  stm32l0xx_it.o(i.LPUART1_IRQHandler)
    LSM6DS3_Init                             0x08002fd5   Thumb Code   158  lsm6ds3.o(i.LSM6DS3_Init)
    LSM6DS3_ReadData                         0x08003095   Thumb Code   228  lsm6ds3.o(i.LSM6DS3_ReadData)
    MX_ADC_Init                              0x08003181   Thumb Code   140  adc.o(i.MX_ADC_Init)
    MX_DMA_Init                              0x08003221   Thumb Code    36  dma.o(i.MX_DMA_Init)
    MX_FREERTOS_Init                         0x08003249   Thumb Code   118  freertos.o(i.MX_FREERTOS_Init)
    MX_GPIO_Init                             0x080032c9   Thumb Code   184  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08003391   Thumb Code    76  i2c.o(i.MX_I2C1_Init)
    MX_LPUART1_UART_Init                     0x080033e9   Thumb Code    46  usart.o(i.MX_LPUART1_UART_Init)
    MX_RTC_Init                              0x08003421   Thumb Code   136  rtc.o(i.MX_RTC_Init)
    MX_SPI1_Init                             0x080034b1   Thumb Code    56  spi.o(i.MX_SPI1_Init)
    MX_USART1_UART_Init                      0x080034f1   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    NMI_Handler                              0x08003529   Thumb Code     2  stm32l0xx_it.o(i.NMI_Handler)
    RTC_ByteToBcd2                           0x0800352b   Thumb Code    22  stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2)
    RTC_EnterInitMode                        0x08003541   Thumb Code    70  stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode)
    RTC_ExitInitMode                         0x08003587   Thumb Code    46  stm32l0xx_hal_rtc.o(i.RTC_ExitInitMode)
    RTC_IRQHandler                           0x080035b5   Thumb Code    10  stm32l0xx_it.o(i.RTC_IRQHandler)
    ReadRegs                                 0x080035c5   Thumb Code    22  lsm6ds3.o(i.ReadRegs)
    SVC_Handler                              0x080035db   Thumb Code     2  port.o(i.SVC_Handler)
    StartAccelTask                           0x080035dd   Thumb Code   422  freertos.o(i.StartAccelTask)
    StartFlashTask                           0x080039d1   Thumb Code     8  freertos.o(i.StartFlashTask)
    StartGM20Task                            0x080039d9   Thumb Code     8  freertos.o(i.StartGM20Task)
    StartGPSTask                             0x080039e1   Thumb Code    26  freertos.o(i.StartGPSTask)
    StartPowerTask                           0x08003a01   Thumb Code     8  freertos.o(i.StartPowerTask)
    SysTick_Handler                          0x08003a09   Thumb Code    32  port.o(i.SysTick_Handler)
    SystemClock_Config                       0x08003a2d   Thumb Code   152  main.o(i.SystemClock_Config)
    SystemInit                               0x08003ac9   Thumb Code     2  system_stm32l0xx.o(i.SystemInit)
    TIM6_IRQHandler                          0x08003acd   Thumb Code    10  stm32l0xx_it.o(i.TIM6_IRQHandler)
    UART_AdvFeatureConfig                    0x08003b4d   Thumb Code   202  stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x08003c19   Thumb Code   190  stm32l0xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08003d4d   Thumb Code   492  stm32l0xx_hal_uart.o(i.UART_SetConfig)
    UART_WaitOnFlagUntilTimeout              0x08003f61   Thumb Code   150  stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x08003ff9   Thumb Code    10  stm32l0xx_it.o(i.USART1_IRQHandler)
    __0printf                                0x08004029   Thumb Code    24  printfa.o(i.__0printf)
    __1printf                                0x08004029   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08004029   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08004029   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08004029   Thumb Code     0  printfa.o(i.__0printf)
    __ARM_clz                                0x08004049   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __ARM_common_switch8                     0x08004077   Thumb Code    26  stm32l0xx_hal_tim.o(i.__ARM_common_switch8)
    __scatterload_copy                       0x080040d5   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080040e3   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080040e5   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    fputc                                    0x080049a1   Thumb Code    20  usart.o(i.fputc)
    main                                     0x080049b9   Thumb Code    50  main.o(i.main)
    osDelay                                  0x080049f7   Thumb Code    16  cmsis_os.o(i.osDelay)
    osKernelStart                            0x08004a07   Thumb Code    10  cmsis_os.o(i.osKernelStart)
    osThreadCreate                           0x08004a11   Thumb Code    88  cmsis_os.o(i.osThreadCreate)
    pvPortMalloc                             0x08004d39   Thumb Code   172  heap_4.o(i.pvPortMalloc)
    pxPortInitialiseStack                    0x08004de9   Thumb Code    28  port.o(i.pxPortInitialiseStack)
    uxListRemove                             0x08004e09   Thumb Code    38  list.o(i.uxListRemove)
    vApplicationGetIdleTaskMemory            0x08004e31   Thumb Code    16  freertos.o(i.vApplicationGetIdleTaskMemory)
    vListInitialise                          0x08004e45   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x08004e5b   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08004e61   Thumb Code    48  list.o(i.vListInsert)
    vListInsertEnd                           0x08004e91   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x08004ea9   Thumb Code    20  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08004ec1   Thumb Code    22  port.o(i.vPortExitCritical)
    vPortFree                                0x08004edd   Thumb Code    62  heap_4.o(i.vPortFree)
    vPortYield                               0x08004f21   Thumb Code    18  port.o(i.vPortYield)
    vTaskDelay                               0x08004f39   Thumb Code    44  tasks.o(i.vTaskDelay)
    vTaskStartScheduler                      0x08004f69   Thumb Code    72  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x08004fc1   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x08004fd1   Thumb Code    78  tasks.o(i.vTaskSwitchContext)
    xPortStartScheduler                      0x08005029   Thumb Code    62  port.o(i.xPortStartScheduler)
    xTaskCreate                              0x08005079   Thumb Code    82  tasks.o(i.xTaskCreate)
    xTaskCreateStatic                        0x080050cb   Thumb Code    68  tasks.o(i.xTaskCreateStatic)
    xTaskIncrementTick                       0x08005111   Thumb Code   180  tasks.o(i.xTaskIncrementTick)
    xTaskResumeAll                           0x080051cd   Thumb Code   164  tasks.o(i.xTaskResumeAll)
    AHBPrescTable                            0x08005304   Data          16  system_stm32l0xx.o(.constdata)
    PLLMulTable                              0x08005314   Data           9  system_stm32l0xx.o(.constdata)
    APBPrescTable                            0x0800531d   Data           8  system_stm32l0xx.o(.constdata)
    Region$$Table$$Base                      0x08005360   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08005380   Number         0  anon$$obj.o(Region$$Table)
    GPSTaskHandle                            0x20000004   Data           4  freertos.o(.data)
    AccelTaskHandle                          0x20000008   Data           4  freertos.o(.data)
    GM20TaskHandle                           0x2000000c   Data           4  freertos.o(.data)
    FlashTaskHandle                          0x20000010   Data           4  freertos.o(.data)
    myPowerTaskHandle                        0x20000014   Data           4  freertos.o(.data)
    uwTickFreq                               0x20000018   Data           1  stm32l0xx_hal.o(.data)
    uwTickPrio                               0x2000001c   Data           4  stm32l0xx_hal.o(.data)
    uwTick                                   0x20000020   Data           4  stm32l0xx_hal.o(.data)
    SystemCoreClock                          0x20000024   Data           4  system_stm32l0xx.o(.data)
    pxCurrentTCB                             0x20000028   Data           4  tasks.o(.data)
    __stdout                                 0x20000080   Data           4  stdout.o(.data)
    adc_values                               0x20000084   Data          12  freertos.o(.bss)
    lsm6ds3_data                             0x20000090   Data          28  freertos.o(.bss)
    GPSTaskBuffer                            0x20000300   Data        2048  freertos.o(.bss)
    GPSTaskControlBlock                      0x20000b00   Data          84  freertos.o(.bss)
    AccelTaskBuffer                          0x20000b54   Data        2048  freertos.o(.bss)
    AccelTaskControlBlock                    0x20001354   Data          84  freertos.o(.bss)
    GM20TaskBuffer                           0x200013a8   Data         512  freertos.o(.bss)
    GM20TaskControlBlock                     0x200015a8   Data          84  freertos.o(.bss)
    FlashTaskBuffer                          0x200015fc   Data         512  freertos.o(.bss)
    FlashTaskControlBlock                    0x200017fc   Data          84  freertos.o(.bss)
    myPowerTaskBuffer                        0x20001850   Data         512  freertos.o(.bss)
    myPowerTaskControlBlock                  0x20001a50   Data          84  freertos.o(.bss)
    hadc                                     0x20001aa4   Data          92  adc.o(.bss)
    hdma_adc                                 0x20001b00   Data          72  adc.o(.bss)
    hi2c1                                    0x20001b48   Data          84  i2c.o(.bss)
    hlpuart1                                 0x20001b9c   Data         136  usart.o(.bss)
    huart1                                   0x20001c24   Data         136  usart.o(.bss)
    hrtc                                     0x20001cac   Data          36  rtc.o(.bss)
    hspi1                                    0x20001cd0   Data          88  spi.o(.bss)
    htim6                                    0x20001d28   Data          64  stm32l0xx_hal_timebase_tim.o(.bss)
    __initial_sp                             0x20002e58   Data           0  startup_stm32l071xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000c1

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00005404, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00005380, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000c0   Data   RO            3    RESET               startup_stm32l071xx.o
    0x080000c0   0x080000c0   0x00000000   Code   RO         5843  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x080000c0   0x080000c0   0x00000004   Code   RO         6149    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x080000c4   0x080000c4   0x00000004   Code   RO         6152    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x080000c8   0x080000c8   0x00000000   Code   RO         6154    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x080000c8   0x080000c8   0x00000000   Code   RO         6156    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x080000c8   0x080000c8   0x00000008   Code   RO         6157    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x080000d0   0x080000d0   0x00000000   Code   RO         6159    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x080000d0   0x080000d0   0x00000000   Code   RO         6161    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x080000d0   0x080000d0   0x00000004   Code   RO         6150    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x080000d4   0x080000d4   0x00000078   Code   RO         5762    .emb_text           port.o
    0x0800014c   0x0800014c   0x0000001c   Code   RO            4    .text               startup_stm32l071xx.o
    0x08000168   0x08000168   0x0000002c   Code   RO         5846    .text               mc_p.l(uidiv.o)
    0x08000194   0x08000194   0x00000060   Code   RO         5848    .text               mc_p.l(uldiv.o)
    0x080001f4   0x080001f4   0x00000024   Code   RO         5850    .text               mc_p.l(memcpya.o)
    0x08000218   0x08000218   0x00000024   Code   RO         5854    .text               mc_p.l(memseta.o)
    0x0800023c   0x0800023c   0x000000b2   Code   RO         6119    .text               mf_p.l(fadd.o)
    0x080002ee   0x080002ee   0x0000007a   Code   RO         6121    .text               mf_p.l(fmul.o)
    0x08000368   0x08000368   0x00000018   Code   RO         6125    .text               mf_p.l(fscalb.o)
    0x08000380   0x08000380   0x00000016   Code   RO         6127    .text               mf_p.l(fflti.o)
    0x08000396   0x08000396   0x0000000e   Code   RO         6129    .text               mf_p.l(ffltui.o)
    0x080003a4   0x080003a4   0x00000028   Code   RO         6131    .text               mf_p.l(f2d.o)
    0x080003cc   0x080003cc   0x00000020   Code   RO         6164    .text               mc_p.l(llshl.o)
    0x080003ec   0x080003ec   0x00000022   Code   RO         6166    .text               mc_p.l(llushr.o)
    0x0800040e   0x0800040e   0x00000000   Code   RO         6175    .text               mc_p.l(iusefp.o)
    0x0800040e   0x0800040e   0x00000082   Code   RO         6176    .text               mf_p.l(fepilogue.o)
    0x08000490   0x08000490   0x00000164   Code   RO         6180    .text               mf_p.l(dadd.o)
    0x080005f4   0x080005f4   0x000000d0   Code   RO         6182    .text               mf_p.l(dmul.o)
    0x080006c4   0x080006c4   0x000000f0   Code   RO         6184    .text               mf_p.l(ddiv.o)
    0x080007b4   0x080007b4   0x00000040   Code   RO         6186    .text               mf_p.l(dfixul.o)
    0x080007f4   0x080007f4   0x00000028   Code   RO         6188    .text               mf_p.l(cdrcmple.o)
    0x0800081c   0x0800081c   0x00000024   Code   RO         6190    .text               mc_p.l(init.o)
    0x08000840   0x08000840   0x00000026   Code   RO         6192    .text               mc_p.l(llsshr.o)
    0x08000866   0x08000866   0x000000be   Code   RO         6194    .text               mf_p.l(depilogue.o)
    0x08000924   0x08000924   0x00000010   Code   RO          346    i.ADC_Calibrate     adc.o
    0x08000934   0x08000934   0x0000006e   Code   RO         1486    i.ADC_DMAConvCplt   stm32l0xx_hal_adc.o
    0x080009a2   0x080009a2   0x0000001a   Code   RO         1487    i.ADC_DMAError      stm32l0xx_hal_adc.o
    0x080009bc   0x080009bc   0x0000000a   Code   RO         1488    i.ADC_DMAHalfConvCplt  stm32l0xx_hal_adc.o
    0x080009c6   0x080009c6   0x00000002   PAD
    0x080009c8   0x080009c8   0x00000024   Code   RO         1489    i.ADC_DelayMicroSecond  stm32l0xx_hal_adc.o
    0x080009ec   0x080009ec   0x00000070   Code   RO         1491    i.ADC_Enable        stm32l0xx_hal_adc.o
    0x08000a5c   0x08000a5c   0x00000010   Code   RO          580    i.DMA1_Channel1_IRQHandler  stm32l0xx_it.o
    0x08000a6c   0x08000a6c   0x0000002c   Code   RO         2931    i.DMA_SetConfig     stm32l0xx_hal_dma.o
    0x08000a98   0x08000a98   0x00000004   Code   RO           12    i.Error_Handler     main.o
    0x08000a9c   0x08000a9c   0x000000b2   Code   RO         1668    i.HAL_ADCEx_Calibration_Start  stm32l0xx_hal_adc_ex.o
    0x08000b4e   0x08000b4e   0x00000002   PAD
    0x08000b50   0x08000b50   0x00000094   Code   RO         1493    i.HAL_ADC_ConfigChannel  stm32l0xx_hal_adc.o
    0x08000be4   0x08000be4   0x00000002   Code   RO         1494    i.HAL_ADC_ConvCpltCallback  stm32l0xx_hal_adc.o
    0x08000be6   0x08000be6   0x00000002   Code   RO         1495    i.HAL_ADC_ConvHalfCpltCallback  stm32l0xx_hal_adc.o
    0x08000be8   0x08000be8   0x00000002   Code   RO         1497    i.HAL_ADC_ErrorCallback  stm32l0xx_hal_adc.o
    0x08000bea   0x08000bea   0x00000002   PAD
    0x08000bec   0x08000bec   0x000001c8   Code   RO         1502    i.HAL_ADC_Init      stm32l0xx_hal_adc.o
    0x08000db4   0x08000db4   0x00000088   Code   RO          348    i.HAL_ADC_MspInit   adc.o
    0x08000e3c   0x08000e3c   0x00000098   Code   RO         1509    i.HAL_ADC_Start_DMA  stm32l0xx_hal_adc.o
    0x08000ed4   0x08000ed4   0x00000044   Code   RO         2932    i.HAL_DMA_Abort     stm32l0xx_hal_dma.o
    0x08000f18   0x08000f18   0x0000004c   Code   RO         2933    i.HAL_DMA_Abort_IT  stm32l0xx_hal_dma.o
    0x08000f64   0x08000f64   0x000000a8   Code   RO         2937    i.HAL_DMA_IRQHandler  stm32l0xx_hal_dma.o
    0x0800100c   0x0800100c   0x00000090   Code   RO         2938    i.HAL_DMA_Init      stm32l0xx_hal_dma.o
    0x0800109c   0x0800109c   0x00000068   Code   RO         2942    i.HAL_DMA_Start_IT  stm32l0xx_hal_dma.o
    0x08001104   0x08001104   0x000001bc   Code   RO         2872    i.HAL_GPIO_Init     stm32l0xx_hal_gpio.o
    0x080012c0   0x080012c0   0x00000010   Code   RO         2875    i.HAL_GPIO_TogglePin  stm32l0xx_hal_gpio.o
    0x080012d0   0x080012d0   0x0000000c   Code   RO         2876    i.HAL_GPIO_WritePin  stm32l0xx_hal_gpio.o
    0x080012dc   0x080012dc   0x0000000c   Code   RO         1735    i.HAL_GetTick       stm32l0xx_hal.o
    0x080012e8   0x080012e8   0x0000004a   Code   RO         2396    i.HAL_I2CEx_ConfigAnalogFilter  stm32l0xx_hal_i2c_ex.o
    0x08001332   0x08001332   0x00000048   Code   RO         2397    i.HAL_I2CEx_ConfigDigitalFilter  stm32l0xx_hal_i2c_ex.o
    0x0800137a   0x0800137a   0x00000002   PAD
    0x0800137c   0x0800137c   0x000000bc   Code   RO         1939    i.HAL_I2C_Init      stm32l0xx_hal_i2c.o
    0x08001438   0x08001438   0x000000f4   Code   RO         1940    i.HAL_I2C_IsDeviceReady  stm32l0xx_hal_i2c.o
    0x0800152c   0x0800152c   0x00000150   Code   RO         1957    i.HAL_I2C_Mem_Read  stm32l0xx_hal_i2c.o
    0x0800167c   0x0800167c   0x0000014c   Code   RO         1960    i.HAL_I2C_Mem_Write  stm32l0xx_hal_i2c.o
    0x080017c8   0x080017c8   0x0000005c   Code   RO          411    i.HAL_I2C_MspInit   i2c.o
    0x08001824   0x08001824   0x00000010   Code   RO         1741    i.HAL_IncTick       stm32l0xx_hal.o
    0x08001834   0x08001834   0x00000028   Code   RO         1742    i.HAL_Init          stm32l0xx_hal.o
    0x0800185c   0x0800185c   0x0000009c   Code   RO          660    i.HAL_InitTick      stm32l0xx_hal_timebase_tim.o
    0x080018f8   0x080018f8   0x00000024   Code   RO          640    i.HAL_MspInit       stm32l0xx_hal_msp.o
    0x0800191c   0x0800191c   0x00000018   Code   RO         3201    i.HAL_NVIC_EnableIRQ  stm32l0xx_hal_cortex.o
    0x08001934   0x08001934   0x00000008   Code   RO         3205    i.HAL_NVIC_SetPriority  stm32l0xx_hal_cortex.o
    0x0800193c   0x0800193c   0x0000017c   Code   RO         2545    i.HAL_RCCEx_PeriphCLKConfig  stm32l0xx_hal_rcc_ex.o
    0x08001ab8   0x08001ab8   0x0000019c   Code   RO         2447    i.HAL_RCC_ClockConfig  stm32l0xx_hal_rcc.o
    0x08001c54   0x08001c54   0x00000040   Code   RO         2450    i.HAL_RCC_GetClockConfig  stm32l0xx_hal_rcc.o
    0x08001c94   0x08001c94   0x00000020   Code   RO         2453    i.HAL_RCC_GetPCLK1Freq  stm32l0xx_hal_rcc.o
    0x08001cb4   0x08001cb4   0x00000020   Code   RO         2454    i.HAL_RCC_GetPCLK2Freq  stm32l0xx_hal_rcc.o
    0x08001cd4   0x08001cd4   0x00000078   Code   RO         2455    i.HAL_RCC_GetSysClockFreq  stm32l0xx_hal_rcc.o
    0x08001d4c   0x08001d4c   0x00000432   Code   RO         2458    i.HAL_RCC_OscConfig  stm32l0xx_hal_rcc.o
    0x0800217e   0x0800217e   0x00000002   PAD
    0x08002180   0x08002180   0x000000ec   Code   RO         4056    i.HAL_RTCEx_SetWakeUpTimer_IT  stm32l0xx_hal_rtc_ex.o
    0x0800226c   0x0800226c   0x00000002   Code   RO         4062    i.HAL_RTCEx_WakeUpTimerEventCallback  stm32l0xx_hal_rtc_ex.o
    0x0800226e   0x0800226e   0x00000002   PAD
    0x08002270   0x08002270   0x0000003c   Code   RO         4063    i.HAL_RTCEx_WakeUpTimerIRQHandler  stm32l0xx_hal_rtc_ex.o
    0x080022ac   0x080022ac   0x000000ac   Code   RO         3873    i.HAL_RTC_Init      stm32l0xx_hal_rtc.o
    0x08002358   0x08002358   0x00000030   Code   RO          505    i.HAL_RTC_MspInit   rtc.o
    0x08002388   0x08002388   0x0000009c   Code   RO         3879    i.HAL_RTC_SetDate   stm32l0xx_hal_rtc.o
    0x08002424   0x08002424   0x000000c0   Code   RO         3880    i.HAL_RTC_SetTime   stm32l0xx_hal_rtc.o
    0x080024e4   0x080024e4   0x00000034   Code   RO         3881    i.HAL_RTC_WaitForSynchro  stm32l0xx_hal_rtc.o
    0x08002518   0x08002518   0x000000b6   Code   RO         4264    i.HAL_SPI_Init      stm32l0xx_hal_spi.o
    0x080025ce   0x080025ce   0x00000002   PAD
    0x080025d0   0x080025d0   0x00000058   Code   RO          543    i.HAL_SPI_MspInit   spi.o
    0x08002628   0x08002628   0x0000003e   Code   RO          762    i.HAL_TIM_Base_Init  stm32l0xx_hal_tim.o
    0x08002666   0x08002666   0x00000002   Code   RO          764    i.HAL_TIM_Base_MspInit  stm32l0xx_hal_tim.o
    0x08002668   0x08002668   0x00000058   Code   RO          767    i.HAL_TIM_Base_Start_IT  stm32l0xx_hal_tim.o
    0x080026c0   0x080026c0   0x00000002   Code   RO          796    i.HAL_TIM_IC_CaptureCallback  stm32l0xx_hal_tim.o
    0x080026c2   0x080026c2   0x000000fc   Code   RO          810    i.HAL_TIM_IRQHandler  stm32l0xx_hal_tim.o
    0x080027be   0x080027be   0x00000002   Code   RO          813    i.HAL_TIM_OC_DelayElapsedCallback  stm32l0xx_hal_tim.o
    0x080027c0   0x080027c0   0x00000002   Code   RO          840    i.HAL_TIM_PWM_PulseFinishedCallback  stm32l0xx_hal_tim.o
    0x080027c2   0x080027c2   0x00000002   PAD
    0x080027c4   0x080027c4   0x00000014   Code   RO           13    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x080027d8   0x080027d8   0x00000002   Code   RO          853    i.HAL_TIM_TriggerCallback  stm32l0xx_hal_tim.o
    0x080027da   0x080027da   0x00000002   Code   RO         3395    i.HAL_UARTEx_RxEventCallback  stm32l0xx_hal_uart.o
    0x080027dc   0x080027dc   0x00000002   Code   RO         3785    i.HAL_UARTEx_WakeupCallback  stm32l0xx_hal_uart_ex.o
    0x080027de   0x080027de   0x00000002   Code   RO         3411    i.HAL_UART_ErrorCallback  stm32l0xx_hal_uart.o
    0x080027e0   0x080027e0   0x000002c0   Code   RO         3414    i.HAL_UART_IRQHandler  stm32l0xx_hal_uart.o
    0x08002aa0   0x08002aa0   0x0000006a   Code   RO         3415    i.HAL_UART_Init     stm32l0xx_hal_uart.o
    0x08002b0a   0x08002b0a   0x00000002   PAD
    0x08002b0c   0x08002b0c   0x000000b0   Code   RO          449    i.HAL_UART_MspInit  usart.o
    0x08002bbc   0x08002bbc   0x000000c0   Code   RO         3424    i.HAL_UART_Transmit  stm32l0xx_hal_uart.o
    0x08002c7c   0x08002c7c   0x00000002   Code   RO         3427    i.HAL_UART_TxCpltCallback  stm32l0xx_hal_uart.o
    0x08002c7e   0x08002c7e   0x00000002   Code   RO          581    i.HardFault_Handler  stm32l0xx_it.o
    0x08002c80   0x08002c80   0x00000022   Code   RO         1986    i.I2C_Flush_TXDR    stm32l0xx_hal_i2c.o
    0x08002ca2   0x08002ca2   0x00000002   PAD
    0x08002ca4   0x08002ca4   0x00000110   Code   RO         1994    i.I2C_IsErrorOccurred  stm32l0xx_hal_i2c.o
    0x08002db4   0x08002db4   0x00000060   Code   RO         1999    i.I2C_RequestMemoryRead  stm32l0xx_hal_i2c.o
    0x08002e14   0x08002e14   0x00000060   Code   RO         2000    i.I2C_RequestMemoryWrite  stm32l0xx_hal_i2c.o
    0x08002e74   0x08002e74   0x0000002c   Code   RO         2003    i.I2C_TransferConfig  stm32l0xx_hal_i2c.o
    0x08002ea0   0x08002ea0   0x00000072   Code   RO         2005    i.I2C_WaitOnFlagUntilTimeout  stm32l0xx_hal_i2c.o
    0x08002f12   0x08002f12   0x00000056   Code   RO         2007    i.I2C_WaitOnSTOPFlagUntilTimeout  stm32l0xx_hal_i2c.o
    0x08002f68   0x08002f68   0x0000005a   Code   RO         2008    i.I2C_WaitOnTXISFlagUntilTimeout  stm32l0xx_hal_i2c.o
    0x08002fc2   0x08002fc2   0x00000002   PAD
    0x08002fc4   0x08002fc4   0x00000010   Code   RO          582    i.LPUART1_IRQHandler  stm32l0xx_it.o
    0x08002fd4   0x08002fd4   0x000000c0   Code   RO          698    i.LSM6DS3_Init      lsm6ds3.o
    0x08003094   0x08003094   0x000000ec   Code   RO          700    i.LSM6DS3_ReadData  lsm6ds3.o
    0x08003180   0x08003180   0x000000a0   Code   RO          349    i.MX_ADC_Init       adc.o
    0x08003220   0x08003220   0x00000028   Code   RO          390    i.MX_DMA_Init       dma.o
    0x08003248   0x08003248   0x00000080   Code   RO          263    i.MX_FREERTOS_Init  freertos.o
    0x080032c8   0x080032c8   0x000000c8   Code   RO          243    i.MX_GPIO_Init      gpio.o
    0x08003390   0x08003390   0x00000058   Code   RO          412    i.MX_I2C1_Init      i2c.o
    0x080033e8   0x080033e8   0x00000038   Code   RO          450    i.MX_LPUART1_UART_Init  usart.o
    0x08003420   0x08003420   0x00000090   Code   RO          506    i.MX_RTC_Init       rtc.o
    0x080034b0   0x080034b0   0x00000040   Code   RO          544    i.MX_SPI1_Init      spi.o
    0x080034f0   0x080034f0   0x00000038   Code   RO          451    i.MX_USART1_UART_Init  usart.o
    0x08003528   0x08003528   0x00000002   Code   RO          583    i.NMI_Handler       stm32l0xx_it.o
    0x0800352a   0x0800352a   0x00000016   Code   RO         3883    i.RTC_ByteToBcd2    stm32l0xx_hal_rtc.o
    0x08003540   0x08003540   0x00000046   Code   RO         3884    i.RTC_EnterInitMode  stm32l0xx_hal_rtc.o
    0x08003586   0x08003586   0x0000002e   Code   RO         3885    i.RTC_ExitInitMode  stm32l0xx_hal_rtc.o
    0x080035b4   0x080035b4   0x00000010   Code   RO          584    i.RTC_IRQHandler    stm32l0xx_it.o
    0x080035c4   0x080035c4   0x00000016   Code   RO          701    i.ReadRegs          lsm6ds3.o
    0x080035da   0x080035da   0x00000002   Code   RO         5763    i.SVC_Handler       port.o
    0x080035dc   0x080035dc   0x000003f4   Code   RO          264    i.StartAccelTask    freertos.o
    0x080039d0   0x080039d0   0x00000008   Code   RO          265    i.StartFlashTask    freertos.o
    0x080039d8   0x080039d8   0x00000008   Code   RO          266    i.StartGM20Task     freertos.o
    0x080039e0   0x080039e0   0x00000020   Code   RO          267    i.StartGPSTask      freertos.o
    0x08003a00   0x08003a00   0x00000008   Code   RO          268    i.StartPowerTask    freertos.o
    0x08003a08   0x08003a08   0x00000024   Code   RO         5764    i.SysTick_Handler   port.o
    0x08003a2c   0x08003a2c   0x0000009c   Code   RO           14    i.SystemClock_Config  main.o
    0x08003ac8   0x08003ac8   0x00000002   Code   RO         4572    i.SystemInit        system_stm32l0xx.o
    0x08003aca   0x08003aca   0x00000002   PAD
    0x08003acc   0x08003acc   0x00000010   Code   RO          585    i.TIM6_IRQHandler   stm32l0xx_it.o
    0x08003adc   0x08003adc   0x00000070   Code   RO          855    i.TIM_Base_SetConfig  stm32l0xx_hal_tim.o
    0x08003b4c   0x08003b4c   0x000000ca   Code   RO         3429    i.UART_AdvFeatureConfig  stm32l0xx_hal_uart.o
    0x08003c16   0x08003c16   0x00000002   PAD
    0x08003c18   0x08003c18   0x000000c4   Code   RO         3430    i.UART_CheckIdleState  stm32l0xx_hal_uart.o
    0x08003cdc   0x08003cdc   0x00000010   Code   RO         3431    i.UART_DMAAbortOnError  stm32l0xx_hal_uart.o
    0x08003cec   0x08003cec   0x00000060   Code   RO         3441    i.UART_EndRxTransfer  stm32l0xx_hal_uart.o
    0x08003d4c   0x08003d4c   0x00000214   Code   RO         3445    i.UART_SetConfig    stm32l0xx_hal_uart.o
    0x08003f60   0x08003f60   0x00000096   Code   RO         3450    i.UART_WaitOnFlagUntilTimeout  stm32l0xx_hal_uart.o
    0x08003ff6   0x08003ff6   0x00000002   PAD
    0x08003ff8   0x08003ff8   0x00000010   Code   RO          586    i.USART1_IRQHandler  stm32l0xx_it.o
    0x08004008   0x08004008   0x0000001e   Code   RO          702    i.WriteReg          lsm6ds3.o
    0x08004026   0x08004026   0x00000002   PAD
    0x08004028   0x08004028   0x00000020   Code   RO         6091    i.__0printf         mc_p.l(printfa.o)
    0x08004048   0x08004048   0x0000002e   Code   RO         6196    i.__ARM_clz         mf_p.l(depilogue.o)
    0x08004076   0x08004076   0x0000001a   Code   RO         1452    i.__ARM_common_switch8  stm32l0xx_hal_tim.o
    0x08004090   0x08004090   0x00000044   Code   RO         3211    i.__NVIC_SetPriority  stm32l0xx_hal_cortex.o
    0x080040d4   0x080040d4   0x0000000e   Code   RO         6200    i.__scatterload_copy  mc_p.l(handlers.o)
    0x080040e2   0x080040e2   0x00000002   Code   RO         6201    i.__scatterload_null  mc_p.l(handlers.o)
    0x080040e4   0x080040e4   0x0000000e   Code   RO         6202    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x080040f2   0x080040f2   0x00000002   PAD
    0x080040f4   0x080040f4   0x00000174   Code   RO         6098    i._fp_digits        mc_p.l(printfa.o)
    0x08004268   0x08004268   0x000006ec   Code   RO         6099    i._printf_core      mc_p.l(printfa.o)
    0x08004954   0x08004954   0x00000020   Code   RO         6100    i._printf_post_padding  mc_p.l(printfa.o)
    0x08004974   0x08004974   0x0000002c   Code   RO         6101    i._printf_pre_padding  mc_p.l(printfa.o)
    0x080049a0   0x080049a0   0x00000018   Code   RO          453    i.fputc             usart.o
    0x080049b8   0x080049b8   0x00000032   Code   RO           15    i.main              main.o
    0x080049ea   0x080049ea   0x0000000c   Code   RO         5364    i.makeFreeRtosPriority  cmsis_os.o
    0x080049f6   0x080049f6   0x00000010   Code   RO         5366    i.osDelay           cmsis_os.o
    0x08004a06   0x08004a06   0x0000000a   Code   RO         5369    i.osKernelStart     cmsis_os.o
    0x08004a10   0x08004a10   0x00000058   Code   RO         5403    i.osThreadCreate    cmsis_os.o
    0x08004a68   0x08004a68   0x00000058   Code   RO         5059    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x08004ac0   0x08004ac0   0x000000b8   Code   RO         5060    i.prvAddNewTaskToReadyList  tasks.o
    0x08004b78   0x08004b78   0x00000026   Code   RO         5061    i.prvDeleteTCB      tasks.o
    0x08004b9e   0x08004b9e   0x00000002   PAD
    0x08004ba0   0x08004ba0   0x00000044   Code   RO         5713    i.prvHeapInit       heap_4.o
    0x08004be4   0x08004be4   0x00000048   Code   RO         5062    i.prvIdleTask       tasks.o
    0x08004c2c   0x08004c2c   0x0000008a   Code   RO         5063    i.prvInitialiseNewTask  tasks.o
    0x08004cb6   0x08004cb6   0x00000002   PAD
    0x08004cb8   0x08004cb8   0x0000004c   Code   RO         5714    i.prvInsertBlockIntoFreeList  heap_4.o
    0x08004d04   0x08004d04   0x00000020   Code   RO         5064    i.prvResetNextTaskUnblockTime  tasks.o
    0x08004d24   0x08004d24   0x00000014   Code   RO         5765    i.prvTaskExitError  port.o
    0x08004d38   0x08004d38   0x000000b0   Code   RO         5715    i.pvPortMalloc      heap_4.o
    0x08004de8   0x08004de8   0x00000020   Code   RO         5766    i.pxPortInitialiseStack  port.o
    0x08004e08   0x08004e08   0x00000026   Code   RO         4698    i.uxListRemove      list.o
    0x08004e2e   0x08004e2e   0x00000002   PAD
    0x08004e30   0x08004e30   0x00000014   Code   RO          269    i.vApplicationGetIdleTaskMemory  freertos.o
    0x08004e44   0x08004e44   0x00000016   Code   RO         4699    i.vListInitialise   list.o
    0x08004e5a   0x08004e5a   0x00000006   Code   RO         4700    i.vListInitialiseItem  list.o
    0x08004e60   0x08004e60   0x00000030   Code   RO         4701    i.vListInsert       list.o
    0x08004e90   0x08004e90   0x00000018   Code   RO         4702    i.vListInsertEnd    list.o
    0x08004ea8   0x08004ea8   0x00000018   Code   RO         5768    i.vPortEnterCritical  port.o
    0x08004ec0   0x08004ec0   0x0000001c   Code   RO         5769    i.vPortExitCritical  port.o
    0x08004edc   0x08004edc   0x00000044   Code   RO         5716    i.vPortFree         heap_4.o
    0x08004f20   0x08004f20   0x00000018   Code   RO         5770    i.vPortYield        port.o
    0x08004f38   0x08004f38   0x00000030   Code   RO         5072    i.vTaskDelay        tasks.o
    0x08004f68   0x08004f68   0x00000058   Code   RO         5085    i.vTaskStartScheduler  tasks.o
    0x08004fc0   0x08004fc0   0x00000010   Code   RO         5087    i.vTaskSuspendAll   tasks.o
    0x08004fd0   0x08004fd0   0x00000058   Code   RO         5088    i.vTaskSwitchContext  tasks.o
    0x08005028   0x08005028   0x00000050   Code   RO         5771    i.xPortStartScheduler  port.o
    0x08005078   0x08005078   0x00000052   Code   RO         5090    i.xTaskCreate       tasks.o
    0x080050ca   0x080050ca   0x00000044   Code   RO         5091    i.xTaskCreateStatic  tasks.o
    0x0800510e   0x0800510e   0x00000002   PAD
    0x08005110   0x08005110   0x000000bc   Code   RO         5098    i.xTaskIncrementTick  tasks.o
    0x080051cc   0x080051cc   0x000000ac   Code   RO         5104    i.xTaskResumeAll    tasks.o
    0x08005278   0x08005278   0x0000008c   Data   RO          281    .constdata          freertos.o
    0x08005304   0x08005304   0x00000019   Data   RO         4573    .constdata          system_stm32l0xx.o
    0x0800531d   0x0800531d   0x00000008   Data   RO         4574    .constdata          system_stm32l0xx.o
    0x08005325   0x08005325   0x00000003   PAD
    0x08005328   0x08005328   0x00000038   Data   RO          282    .conststring        freertos.o
    0x08005360   0x08005360   0x00000020   Data   RO         6198    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08005380, Size: 0x00002e58, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08005380   0x00000018   Data   RW          283    .data               freertos.o
    0x20000018   0x08005398   0x0000000c   Data   RW         1753    .data               stm32l0xx_hal.o
    0x20000024   0x080053a4   0x00000004   Data   RW         4575    .data               system_stm32l0xx.o
    0x20000028   0x080053a8   0x0000003c   Data   RW         5107    .data               tasks.o
    0x20000064   0x080053e4   0x00000018   Data   RW         5721    .data               heap_4.o
    0x2000007c   0x080053fc   0x00000004   Data   RW         5772    .data               port.o
    0x20000080   0x08005400   0x00000004   Data   RW         6163    .data               mc_p.l(stdout.o)
    0x20000084        -       0x0000027c   Zero   RW          270    .bss                freertos.o
    0x20000300        -       0x00000800   Zero   RW          271    .bss                freertos.o
    0x20000b00        -       0x00000054   Zero   RW          272    .bss                freertos.o
    0x20000b54        -       0x00000800   Zero   RW          273    .bss                freertos.o
    0x20001354        -       0x00000054   Zero   RW          274    .bss                freertos.o
    0x200013a8        -       0x00000200   Zero   RW          275    .bss                freertos.o
    0x200015a8        -       0x00000054   Zero   RW          276    .bss                freertos.o
    0x200015fc        -       0x00000200   Zero   RW          277    .bss                freertos.o
    0x200017fc        -       0x00000054   Zero   RW          278    .bss                freertos.o
    0x20001850        -       0x00000200   Zero   RW          279    .bss                freertos.o
    0x20001a50        -       0x00000054   Zero   RW          280    .bss                freertos.o
    0x20001aa4        -       0x000000a4   Zero   RW          350    .bss                adc.o
    0x20001b48        -       0x00000054   Zero   RW          413    .bss                i2c.o
    0x20001b9c        -       0x00000110   Zero   RW          454    .bss                usart.o
    0x20001cac        -       0x00000024   Zero   RW          507    .bss                rtc.o
    0x20001cd0        -       0x00000058   Zero   RW          545    .bss                spi.o
    0x20001d28        -       0x00000040   Zero   RW          663    .bss                stm32l0xx_hal_timebase_tim.o
    0x20001d68        -       0x000000f0   Zero   RW         5106    .bss                tasks.o
    0x20001e58        -       0x00000c00   Zero   RW         5720    .bss                heap_4.o
    0x20002a58        -       0x00000400   Zero   RW            1    STACK               startup_stm32l071xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       312         40          0          0        164       2268   adc.o
       126          0          0          0          0       8349   cmsis_os.o
        40          4          0          0          0        762   dma.o
         0          0          0          0          0      10696   event_groups.o
      1216        610        196         24       6688       6294   freertos.o
       200         16          0          0          0        959   gpio.o
       388         22          0         24       3072       4235   heap_4.o
       180         24          0          0         84       1637   i2c.o
       138          0          0          0          0       3418   list.o
       480         42          0          0          0       2925   lsm6ds3.o
       230          8          0          0          0     428656   main.o
       366         58          0          4          0       6988   port.o
       192         16          0          0         36       1599   rtc.o
       152         16          0          0         88       1625   spi.o
        28          8        192          0       1024        640   startup_stm32l071xx.o
        68         14          0         12          0       7954   stm32l0xx_hal.o
      1056         48          0          0          0       7913   stm32l0xx_hal_adc.o
       178          0          0          0          0       1438   stm32l0xx_hal_adc_ex.o
       100         14          0          0          0      11364   stm32l0xx_hal_cortex.o
       604         12          0          0          0       5018   stm32l0xx_hal_dma.o
         0          0          0          0          0       2472   stm32l0xx_hal_flash.o
       472         32          0          0          0       2745   stm32l0xx_hal_gpio.o
      1932         50          0          0          0      13298   stm32l0xx_hal_i2c.o
       146          0          0          0          0       1923   stm32l0xx_hal_i2c_ex.o
        36          4          0          0          0        474   stm32l0xx_hal_msp.o
      1734        108          0          0          0       6329   stm32l0xx_hal_rcc.o
       380         12          0          0          0       1464   stm32l0xx_hal_rcc_ex.o
       710         18          0          0          0       5983   stm32l0xx_hal_rtc.o
       298         20          0          0          0       2669   stm32l0xx_hal_rtc_ex.o
       182          0          0          0          0       1227   stm32l0xx_hal_spi.o
       550         28          0          0          0       6921   stm32l0xx_hal_tim.o
       156         24          0          0         64       1079   stm32l0xx_hal_timebase_tim.o
      2200         82          0          0          0      13655   stm32l0xx_hal_uart.o
         2          0          0          0          0       1024   stm32l0xx_hal_uart_ex.o
        84         30          0          0          0       3201   stm32l0xx_it.o
         0          0          0          0          0       9616   stream_buffer.o
         2          0         33          4          0       1195   system_stm32l0xx.o
      1302         82          0         60        240      18078   tasks.o
       312         36          0          0        272       2814   usart.o

    ----------------------------------------------------------------------
     16590       <USER>        <GROUP>        128      11732     610905   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        38          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0         60   memcpya.o
        36          0          0          0          0        100   memseta.o
      2252         98          0          0          0        412   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         72   uidiv.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        64         10          0          0          0         68   dfixul.o
       208          6          0          0          0         88   dmul.o
        40          0          0          0          0         60   f2d.o
       178          0          0          0          0        108   fadd.o
       130          0          0          0          0        144   fepilogue.o
        22          0          0          0          0         68   fflti.o
        14          0          0          0          0         68   ffltui.o
       122          0          0          0          0         72   fmul.o
        24          0          0          0          0         60   fscalb.o

    ----------------------------------------------------------------------
      4330        <USER>          <GROUP>          4          0       2244   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2654        114          0          4          0       1000   mc_p.l
      1674         28          0          0          0       1244   mf_p.l

    ----------------------------------------------------------------------
      4330        <USER>          <GROUP>          4          0       2244   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     20920       1620        456        132      11732     605321   Grand Totals
     20920       1620        456        132      11732     605321   ELF Image Totals
     20920       1620        456        132          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                21376 (  20.88kB)
    Total RW  Size (RW Data + ZI Data)             11864 (  11.59kB)
    Total ROM Size (Code + RO Data + RW Data)      21508 (  21.00kB)

==============================================================================

