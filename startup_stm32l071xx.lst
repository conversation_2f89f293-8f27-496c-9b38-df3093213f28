


ARM Macro Assembler    Page 1 


    1 00000000         ;*******************************************************
                       ***********************
    2 00000000         ;* File Name          : startup_stm32l071xx.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* Description        : STM32l071xx Devices vector table
                        for MDK-ARM toolchain.
    5 00000000         ;*                      This module performs:
    6 00000000         ;*                      - Set the initial SP
    7 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
    8 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
    9 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   10 00000000         ;*                        calls main()).
   11 00000000         ;*                      After Reset the Cortex-M0+ proce
                       ssor is in Thread mode,
   12 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   13 00000000         ;*******************************************************
                       ***********************
   14 00000000         ;* @attention
   15 00000000         ;*
   16 00000000         ;* Copyright (c) 2016 STMicroelectronics.
   17 00000000         ;* All rights reserved.
   18 00000000         ;*
   19 00000000         ;* This software is licensed under terms that can be fou
                       nd in the LICENSE file
   20 00000000         ;* in the root directory of this software component.
   21 00000000         ;* If no LICENSE file comes with this software, it is pr
                       ovided AS-IS.
   22 00000000         ;*
   23 00000000         ;*******************************************************
                       ***********************
   24 00000000         
   25 00000000         ; Amount of memory (in bytes) allocated for Stack
   26 00000000         ; Tailor this value to your application needs
   27 00000000         ; <h> Stack Configuration
   28 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   29 00000000         ; </h>
   30 00000000         
   31 00000000 00000400 
                       Stack_Size
                               EQU              0x400
   32 00000000         
   33 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   34 00000000         Stack_Mem
                               SPACE            Stack_Size
   35 00000400         __initial_sp
   36 00000400         
   37 00000400         
   38 00000400         ; <h> Heap Configuration
   39 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   40 00000400         ; </h>
   41 00000400         
   42 00000400 00000200 
                       Heap_Size
                               EQU              0x200



ARM Macro Assembler    Page 2 


   43 00000400         
   44 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   45 00000000         __heap_base
   46 00000000         Heap_Mem
                               SPACE            Heap_Size
   47 00000200         __heap_limit
   48 00000200         
   49 00000200                 PRESERVE8
   50 00000200                 THUMB
   51 00000200         
   52 00000200         
   53 00000200         ; Vector Table Mapped to Address 0 at Reset
   54 00000200                 AREA             RESET, DATA, READONLY
   55 00000000                 EXPORT           __Vectors
   56 00000000                 EXPORT           __Vectors_End
   57 00000000                 EXPORT           __Vectors_Size
   58 00000000         
   59 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   60 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   61 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   62 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   63 00000010 00000000        DCD              0           ; Reserved
   64 00000014 00000000        DCD              0           ; Reserved
   65 00000018 00000000        DCD              0           ; Reserved
   66 0000001C 00000000        DCD              0           ; Reserved
   67 00000020 00000000        DCD              0           ; Reserved
   68 00000024 00000000        DCD              0           ; Reserved
   69 00000028 00000000        DCD              0           ; Reserved
   70 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   71 00000030 00000000        DCD              0           ; Reserved
   72 00000034 00000000        DCD              0           ; Reserved
   73 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   74 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   75 00000040         
   76 00000040         ; External Interrupts
   77 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window Watchdog
   78 00000044 00000000        DCD              PVD_IRQHandler ; PVD through EX
                                                            TI Line detect
   79 00000048 00000000        DCD              RTC_IRQHandler ; RTC through EX
                                                            TI Line
   80 0000004C 00000000        DCD              FLASH_IRQHandler ; FLASH
   81 00000050 00000000        DCD              RCC_IRQHandler ; RCC
   82 00000054 00000000        DCD              EXTI0_1_IRQHandler 
                                                            ; EXTI Line 0 and 1
                                                            
   83 00000058 00000000        DCD              EXTI2_3_IRQHandler 
                                                            ; EXTI Line 2 and 3
                                                            
   84 0000005C 00000000        DCD              EXTI4_15_IRQHandler 
                                                            ; EXTI Line 4 to 15
                                                            
   85 00000060 00000000        DCD              0           ; Reserved



ARM Macro Assembler    Page 3 


   86 00000064 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   87 00000068 00000000        DCD              DMA1_Channel2_3_IRQHandler ; DM
                                                            A1 Channel 2 and Ch
                                                            annel 3
   88 0000006C 00000000        DCD              DMA1_Channel4_5_6_7_IRQHandler 
                                                            ; DMA1 Channel 4, C
                                                            hannel 5, Channel 6
                                                             and Channel 7
   89 00000070 00000000        DCD              ADC1_COMP_IRQHandler ; ADC1, CO
                                                            MP1 and COMP2 
   90 00000074 00000000        DCD              LPTIM1_IRQHandler ; LPTIM1
   91 00000078 00000000        DCD              USART4_5_IRQHandler 
                                                            ; USART4 and USART5
                                                            
   92 0000007C 00000000        DCD              TIM2_IRQHandler ; TIM2
   93 00000080 00000000        DCD              TIM3_IRQHandler ; TIM3
   94 00000084 00000000        DCD              TIM6_IRQHandler ; TIM6
   95 00000088 00000000        DCD              TIM7_IRQHandler ; TIM7
   96 0000008C 00000000        DCD              0           ; Reserved
   97 00000090 00000000        DCD              TIM21_IRQHandler ; TIM21
   98 00000094 00000000        DCD              I2C3_IRQHandler ; I2C3
   99 00000098 00000000        DCD              TIM22_IRQHandler ; TIM22
  100 0000009C 00000000        DCD              I2C1_IRQHandler ; I2C1
  101 000000A0 00000000        DCD              I2C2_IRQHandler ; I2C2
  102 000000A4 00000000        DCD              SPI1_IRQHandler ; SPI1
  103 000000A8 00000000        DCD              SPI2_IRQHandler ; SPI2
  104 000000AC 00000000        DCD              USART1_IRQHandler ; USART1
  105 000000B0 00000000        DCD              USART2_IRQHandler ; USART2
  106 000000B4 00000000        DCD              LPUART1_IRQHandler ; LPUART1
  107 000000B8 00000000        DCD              0           ; Reserved
  108 000000BC 00000000        DCD              0           ; Reserved
  109 000000C0         
  110 000000C0         __Vectors_End
  111 000000C0         
  112 000000C0 000000C0 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  113 000000C0         
  114 000000C0                 AREA             |.text|, CODE, READONLY
  115 00000000         
  116 00000000         ; Reset handler routine
  117 00000000         Reset_Handler
                               PROC
  118 00000000                 EXPORT           Reset_Handler                 [
WEAK]
  119 00000000                 IMPORT           __main
  120 00000000                 IMPORT           SystemInit
  121 00000000 4804            LDR              R0, =SystemInit
  122 00000002 4780            BLX              R0
  123 00000004 4804            LDR              R0, =__main
  124 00000006 4700            BX               R0
  125 00000008                 ENDP
  126 00000008         
  127 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  128 00000008         
  129 00000008         NMI_Handler
                               PROC



ARM Macro Assembler    Page 4 


  130 00000008                 EXPORT           NMI_Handler                    
[WEAK]
  131 00000008 E7FE            B                .
  132 0000000A                 ENDP
  134 0000000A         HardFault_Handler
                               PROC
  135 0000000A                 EXPORT           HardFault_Handler              
[WEAK]
  136 0000000A E7FE            B                .
  137 0000000C                 ENDP
  138 0000000C         SVC_Handler
                               PROC
  139 0000000C                 EXPORT           SVC_Handler                    
[WEAK]
  140 0000000C E7FE            B                .
  141 0000000E                 ENDP
  142 0000000E         PendSV_Handler
                               PROC
  143 0000000E                 EXPORT           PendSV_Handler                 
[WEAK]
  144 0000000E E7FE            B                .
  145 00000010                 ENDP
  146 00000010         SysTick_Handler
                               PROC
  147 00000010                 EXPORT           SysTick_Handler                
[WEAK]
  148 00000010 E7FE            B                .
  149 00000012                 ENDP
  150 00000012         
  151 00000012         Default_Handler
                               PROC
  152 00000012         
  153 00000012                 EXPORT           WWDG_IRQHandler                
[WEAK]
  154 00000012                 EXPORT           PVD_IRQHandler                 
[WEAK]
  155 00000012                 EXPORT           RTC_IRQHandler                 
[WEAK]
  156 00000012                 EXPORT           FLASH_IRQHandler               
[WEAK]
  157 00000012                 EXPORT           RCC_IRQHandler                 
[WEAK]
  158 00000012                 EXPORT           EXTI0_1_IRQHandler             
[WEAK]
  159 00000012                 EXPORT           EXTI2_3_IRQHandler             
[WEAK]
  160 00000012                 EXPORT           EXTI4_15_IRQHandler            
[WEAK]
  161 00000012                 EXPORT           DMA1_Channel1_IRQHandler       
[WEAK]
  162 00000012                 EXPORT           DMA1_Channel2_3_IRQHandler     
[WEAK]
  163 00000012                 EXPORT           DMA1_Channel4_5_6_7_IRQHandler 
[WEAK]
  164 00000012                 EXPORT           ADC1_COMP_IRQHandler           
[WEAK]
  165 00000012                 EXPORT           LPTIM1_IRQHandler              
[WEAK]
  166 00000012                 EXPORT           USART4_5_IRQHandler            



ARM Macro Assembler    Page 5 


[WEAK]
  167 00000012                 EXPORT           TIM2_IRQHandler                
[WEAK]
  168 00000012                 EXPORT           TIM3_IRQHandler                
[WEAK]
  169 00000012                 EXPORT           TIM6_IRQHandler                
[WEAK]
  170 00000012                 EXPORT           TIM7_IRQHandler                
[WEAK]
  171 00000012                 EXPORT           TIM21_IRQHandler               
[WEAK]
  172 00000012                 EXPORT           TIM22_IRQHandler               
[WEAK]
  173 00000012                 EXPORT           I2C1_IRQHandler                
[WEAK]
  174 00000012                 EXPORT           I2C2_IRQHandler                
[WEAK]
  175 00000012                 EXPORT           I2C3_IRQHandler                
[WEAK]
  176 00000012                 EXPORT           SPI1_IRQHandler                
[WEAK]
  177 00000012                 EXPORT           SPI2_IRQHandler                
[WEAK]
  178 00000012                 EXPORT           USART1_IRQHandler              
[WEAK]
  179 00000012                 EXPORT           USART2_IRQHandler              
[WEAK]
  180 00000012                 EXPORT           LPUART1_IRQHandler             
[WEAK]
  181 00000012         
  182 00000012         WWDG_IRQHandler
  183 00000012         PVD_IRQHandler
  184 00000012         RTC_IRQHandler
  185 00000012         FLASH_IRQHandler
  186 00000012         RCC_IRQHandler
  187 00000012         EXTI0_1_IRQHandler
  188 00000012         EXTI2_3_IRQHandler
  189 00000012         EXTI4_15_IRQHandler
  190 00000012         DMA1_Channel1_IRQHandler
  191 00000012         DMA1_Channel2_3_IRQHandler
  192 00000012         DMA1_Channel4_5_6_7_IRQHandler
  193 00000012         ADC1_COMP_IRQHandler
  194 00000012         LPTIM1_IRQHandler
  195 00000012         USART4_5_IRQHandler
  196 00000012         TIM2_IRQHandler
  197 00000012         TIM3_IRQHandler
  198 00000012         TIM6_IRQHandler
  199 00000012         TIM7_IRQHandler
  200 00000012         TIM21_IRQHandler
  201 00000012         TIM22_IRQHandler
  202 00000012         I2C1_IRQHandler
  203 00000012         I2C2_IRQHandler
  204 00000012         I2C3_IRQHandler
  205 00000012         SPI1_IRQHandler
  206 00000012         SPI2_IRQHandler
  207 00000012         USART1_IRQHandler
  208 00000012         USART2_IRQHandler
  209 00000012         LPUART1_IRQHandler
  210 00000012         



ARM Macro Assembler    Page 6 


  211 00000012 E7FE            B                .
  212 00000014         
  213 00000014                 ENDP
  214 00000014         
  215 00000014                 ALIGN
  216 00000014         
  217 00000014         ;*******************************************************
                       ************************
  218 00000014         ; User Stack and Heap initialization
  219 00000014         ;*******************************************************
                       ************************
  220 00000014                 IF               :DEF:__MICROLIB
  221 00000014         
  222 00000014                 EXPORT           __initial_sp
  223 00000014                 EXPORT           __heap_base
  224 00000014                 EXPORT           __heap_limit
  225 00000014         
  226 00000014                 ELSE
  241                          ENDIF
  242 00000014         
  243 00000014                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0+ --apcs=inter
work --depend=test\startup_stm32l071xx.d -otest\startup_stm32l071xx.o -I../Core
/Inc -I.\RTE\_TEST -IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.4.0\CMSIS\Core\Include -IC
:\Keil_v5\ARM\PACK\Keil\STM32L0xx_DFP\2.1.0\Drivers\CMSIS\Device\ST\STM32L0xx\I
nclude --predefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSION SETA 529"
 --predefine="_RTE_ SETA 1" --predefine="STM32L071xx SETA 1" --list=startup_stm
32l071xx.lst startup_stm32l071xx.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 33 in file startup_stm32l071xx.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 34 in file startup_stm32l071xx.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 35 in file startup_stm32l071xx.s
   Uses
      At line 59 in file startup_stm32l071xx.s
      At line 222 in file startup_stm32l071xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 44 in file startup_stm32l071xx.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 46 in file startup_stm32l071xx.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 45 in file startup_stm32l071xx.s
   Uses
      At line 223 in file startup_stm32l071xx.s
Comment: __heap_base used once
__heap_limit 00000200

Symbol: __heap_limit
   Definitions
      At line 47 in file startup_stm32l071xx.s
   Uses
      At line 224 in file startup_stm32l071xx.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 54 in file startup_stm32l071xx.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 59 in file startup_stm32l071xx.s
   Uses
      At line 55 in file startup_stm32l071xx.s
      At line 112 in file startup_stm32l071xx.s

__Vectors_End 000000C0

Symbol: __Vectors_End
   Definitions
      At line 110 in file startup_stm32l071xx.s
   Uses
      At line 56 in file startup_stm32l071xx.s
      At line 112 in file startup_stm32l071xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 114 in file startup_stm32l071xx.s
   Uses
      None
Comment: .text unused
ADC1_COMP_IRQHandler 00000012

Symbol: ADC1_COMP_IRQHandler
   Definitions
      At line 193 in file startup_stm32l071xx.s
   Uses
      At line 89 in file startup_stm32l071xx.s
      At line 164 in file startup_stm32l071xx.s

DMA1_Channel1_IRQHandler 00000012

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 190 in file startup_stm32l071xx.s
   Uses
      At line 86 in file startup_stm32l071xx.s
      At line 161 in file startup_stm32l071xx.s

DMA1_Channel2_3_IRQHandler 00000012

Symbol: DMA1_Channel2_3_IRQHandler
   Definitions
      At line 191 in file startup_stm32l071xx.s
   Uses
      At line 87 in file startup_stm32l071xx.s
      At line 162 in file startup_stm32l071xx.s

DMA1_Channel4_5_6_7_IRQHandler 00000012

Symbol: DMA1_Channel4_5_6_7_IRQHandler
   Definitions
      At line 192 in file startup_stm32l071xx.s
   Uses
      At line 88 in file startup_stm32l071xx.s
      At line 163 in file startup_stm32l071xx.s

Default_Handler 00000012

Symbol: Default_Handler
   Definitions
      At line 151 in file startup_stm32l071xx.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_1_IRQHandler 00000012

Symbol: EXTI0_1_IRQHandler
   Definitions
      At line 187 in file startup_stm32l071xx.s
   Uses
      At line 82 in file startup_stm32l071xx.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 158 in file startup_stm32l071xx.s

EXTI2_3_IRQHandler 00000012

Symbol: EXTI2_3_IRQHandler
   Definitions
      At line 188 in file startup_stm32l071xx.s
   Uses
      At line 83 in file startup_stm32l071xx.s
      At line 159 in file startup_stm32l071xx.s

EXTI4_15_IRQHandler 00000012

Symbol: EXTI4_15_IRQHandler
   Definitions
      At line 189 in file startup_stm32l071xx.s
   Uses
      At line 84 in file startup_stm32l071xx.s
      At line 160 in file startup_stm32l071xx.s

FLASH_IRQHandler 00000012

Symbol: FLASH_IRQHandler
   Definitions
      At line 185 in file startup_stm32l071xx.s
   Uses
      At line 80 in file startup_stm32l071xx.s
      At line 156 in file startup_stm32l071xx.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 134 in file startup_stm32l071xx.s
   Uses
      At line 62 in file startup_stm32l071xx.s
      At line 135 in file startup_stm32l071xx.s

I2C1_IRQHandler 00000012

Symbol: I2C1_IRQHandler
   Definitions
      At line 202 in file startup_stm32l071xx.s
   Uses
      At line 100 in file startup_stm32l071xx.s
      At line 173 in file startup_stm32l071xx.s

I2C2_IRQHandler 00000012

Symbol: I2C2_IRQHandler
   Definitions
      At line 203 in file startup_stm32l071xx.s
   Uses
      At line 101 in file startup_stm32l071xx.s
      At line 174 in file startup_stm32l071xx.s

I2C3_IRQHandler 00000012

Symbol: I2C3_IRQHandler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 204 in file startup_stm32l071xx.s
   Uses
      At line 98 in file startup_stm32l071xx.s
      At line 175 in file startup_stm32l071xx.s

LPTIM1_IRQHandler 00000012

Symbol: LPTIM1_IRQHandler
   Definitions
      At line 194 in file startup_stm32l071xx.s
   Uses
      At line 90 in file startup_stm32l071xx.s
      At line 165 in file startup_stm32l071xx.s

LPUART1_IRQHandler 00000012

Symbol: LPUART1_IRQHandler
   Definitions
      At line 209 in file startup_stm32l071xx.s
   Uses
      At line 106 in file startup_stm32l071xx.s
      At line 180 in file startup_stm32l071xx.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 129 in file startup_stm32l071xx.s
   Uses
      At line 61 in file startup_stm32l071xx.s
      At line 130 in file startup_stm32l071xx.s

PVD_IRQHandler 00000012

Symbol: PVD_IRQHandler
   Definitions
      At line 183 in file startup_stm32l071xx.s
   Uses
      At line 78 in file startup_stm32l071xx.s
      At line 154 in file startup_stm32l071xx.s

PendSV_Handler 0000000E

Symbol: PendSV_Handler
   Definitions
      At line 142 in file startup_stm32l071xx.s
   Uses
      At line 73 in file startup_stm32l071xx.s
      At line 143 in file startup_stm32l071xx.s

RCC_IRQHandler 00000012

Symbol: RCC_IRQHandler
   Definitions
      At line 186 in file startup_stm32l071xx.s
   Uses
      At line 81 in file startup_stm32l071xx.s
      At line 157 in file startup_stm32l071xx.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


RTC_IRQHandler 00000012

Symbol: RTC_IRQHandler
   Definitions
      At line 184 in file startup_stm32l071xx.s
   Uses
      At line 79 in file startup_stm32l071xx.s
      At line 155 in file startup_stm32l071xx.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 117 in file startup_stm32l071xx.s
   Uses
      At line 60 in file startup_stm32l071xx.s
      At line 118 in file startup_stm32l071xx.s

SPI1_IRQHandler 00000012

Symbol: SPI1_IRQHandler
   Definitions
      At line 205 in file startup_stm32l071xx.s
   Uses
      At line 102 in file startup_stm32l071xx.s
      At line 176 in file startup_stm32l071xx.s

SPI2_IRQHandler 00000012

Symbol: SPI2_IRQHandler
   Definitions
      At line 206 in file startup_stm32l071xx.s
   Uses
      At line 103 in file startup_stm32l071xx.s
      At line 177 in file startup_stm32l071xx.s

SVC_Handler 0000000C

Symbol: SVC_Handler
   Definitions
      At line 138 in file startup_stm32l071xx.s
   Uses
      At line 70 in file startup_stm32l071xx.s
      At line 139 in file startup_stm32l071xx.s

SysTick_Handler 00000010

Symbol: SysTick_Handler
   Definitions
      At line 146 in file startup_stm32l071xx.s
   Uses
      At line 74 in file startup_stm32l071xx.s
      At line 147 in file startup_stm32l071xx.s

TIM21_IRQHandler 00000012

Symbol: TIM21_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 200 in file startup_stm32l071xx.s
   Uses
      At line 97 in file startup_stm32l071xx.s
      At line 171 in file startup_stm32l071xx.s

TIM22_IRQHandler 00000012

Symbol: TIM22_IRQHandler
   Definitions
      At line 201 in file startup_stm32l071xx.s
   Uses
      At line 99 in file startup_stm32l071xx.s
      At line 172 in file startup_stm32l071xx.s

TIM2_IRQHandler 00000012

Symbol: TIM2_IRQHandler
   Definitions
      At line 196 in file startup_stm32l071xx.s
   Uses
      At line 92 in file startup_stm32l071xx.s
      At line 167 in file startup_stm32l071xx.s

TIM3_IRQHandler 00000012

Symbol: TIM3_IRQHandler
   Definitions
      At line 197 in file startup_stm32l071xx.s
   Uses
      At line 93 in file startup_stm32l071xx.s
      At line 168 in file startup_stm32l071xx.s

TIM6_IRQHandler 00000012

Symbol: TIM6_IRQHandler
   Definitions
      At line 198 in file startup_stm32l071xx.s
   Uses
      At line 94 in file startup_stm32l071xx.s
      At line 169 in file startup_stm32l071xx.s

TIM7_IRQHandler 00000012

Symbol: TIM7_IRQHandler
   Definitions
      At line 199 in file startup_stm32l071xx.s
   Uses
      At line 95 in file startup_stm32l071xx.s
      At line 170 in file startup_stm32l071xx.s

USART1_IRQHandler 00000012

Symbol: USART1_IRQHandler
   Definitions
      At line 207 in file startup_stm32l071xx.s
   Uses
      At line 104 in file startup_stm32l071xx.s
      At line 178 in file startup_stm32l071xx.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

USART2_IRQHandler 00000012

Symbol: USART2_IRQHandler
   Definitions
      At line 208 in file startup_stm32l071xx.s
   Uses
      At line 105 in file startup_stm32l071xx.s
      At line 179 in file startup_stm32l071xx.s

USART4_5_IRQHandler 00000012

Symbol: USART4_5_IRQHandler
   Definitions
      At line 195 in file startup_stm32l071xx.s
   Uses
      At line 91 in file startup_stm32l071xx.s
      At line 166 in file startup_stm32l071xx.s

WWDG_IRQHandler 00000012

Symbol: WWDG_IRQHandler
   Definitions
      At line 182 in file startup_stm32l071xx.s
   Uses
      At line 77 in file startup_stm32l071xx.s
      At line 153 in file startup_stm32l071xx.s

36 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000200

Symbol: Heap_Size
   Definitions
      At line 42 in file startup_stm32l071xx.s
   Uses
      At line 46 in file startup_stm32l071xx.s
Comment: Heap_Size used once
Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 31 in file startup_stm32l071xx.s
   Uses
      At line 34 in file startup_stm32l071xx.s
Comment: Stack_Size used once
__Vectors_Size 000000C0

Symbol: __Vectors_Size
   Definitions
      At line 112 in file startup_stm32l071xx.s
   Uses
      At line 57 in file startup_stm32l071xx.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 120 in file startup_stm32l071xx.s
   Uses
      At line 121 in file startup_stm32l071xx.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 119 in file startup_stm32l071xx.s
   Uses
      At line 123 in file startup_stm32l071xx.s
Comment: __main used once
2 symbols
387 symbols in table
